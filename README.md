# Transcript Analysis Dashboard

A comprehensive web application for analyzing voice bot conversation transcripts with policy accuracy tracking and conversation flow analysis.

## Features

- **File Upload**: Upload PDF, TXT, or DOCX transcript files
- **Real-time Analysis**: Live analysis of conversation transcripts
- **Policy Accuracy Tracking**: Compare expected vs actual policy recommendations
- **Conversation Flow Analysis**: Track step completion and adherence
- **Individual Call Analysis**: Detailed breakdown of each customer interaction
- **Visual Dashboard**: Modern, responsive UI with interactive charts

## Prerequisites

- Node.js (v16 or higher)
- Python (v3.8 or higher)
- pip (Python package manager)

## Installation & Setup

### 1. Install Frontend Dependencies
```bash
npm install
```

### 2. Install Python Dependencies
```bash
pip install -r requirements.txt
```

## Running the Application

### Option 1: Using the Startup Script (Recommended)
```bash
# Windows (PowerShell)
.\start.ps1

# Windows (Command Prompt)
start.bat
```

### Option 2: Manual Startup

#### Start the Flask API Server
```bash
python api.py
```
The API will be available at `http://localhost:5000`

#### Start the React Development Server
```bash
npm run dev
```
The frontend will be available at `http://localhost:5173`

## Usage

1. **Open the Application**: Navigate to `http://localhost:5173` in your browser
2. **Upload Transcripts**: Click the upload area and select your transcript files (PDF, TXT, or DOCX)
3. **View Analysis**: The system will automatically analyze the transcripts and display results
4. **Navigate Tabs**: Use the tabs to view different analysis perspectives:
   - **Overview**: High-level metrics and summary
   - **Individual Calls**: Detailed analysis of each customer call
   - **Flow Analysis**: Conversation flow and step adherence
   - **Policy Analysis**: Policy recommendation accuracy

## Sample Data

A sample transcript file (`sample_transcript.txt`) is included for testing. This file contains three example conversations that demonstrate the analysis capabilities.

## API Endpoints

- `POST /api/analyze`: Upload and analyze transcript files
- `GET /api/health`: Health check endpoint

## File Formats Supported

- **PDF**: Transcript documents in PDF format
- **TXT**: Plain text transcript files
- **DOCX**: Microsoft Word documents

## Analysis Features

### Customer Data Extraction
- Name, age, pincode, family members
- Income information
- Health conditions

### Conversation Flow Analysis
- Step completion tracking
- Adherence scoring
- Flow path visualization

### Policy Analysis
- Expected vs actual policy recommendations
- Confidence scoring
- Match/mismatch detection

## Project Structure

```
ABHI_CPA-1/
├── api.py                 # Flask API server
├── requirements.txt       # Python dependencies
├── sample_transcript.txt  # Sample data for testing
├── start.bat             # Windows batch startup script
├── start.ps1             # PowerShell startup script
├── src/
│   ├── components/
│   │   └── TranscriptAnalyzer.tsx  # Main React component
│   └── utils/
│       └── transcriptAnalyzer.py   # Python analysis engine
└── package.json          # Node.js dependencies
```

## Troubleshooting

### Common Issues

1. **Port Already in Use**: If port 5000 or 5173 is already in use, the startup script will show an error. Close other applications using these ports.

2. **Python Dependencies**: If you encounter import errors, ensure all Python dependencies are installed:
   ```bash
   pip install -r requirements.txt
   ```

3. **CORS Issues**: The API includes CORS headers, but if you encounter CORS errors, ensure the API is running on `http://localhost:5000`.

4. **File Upload Issues**: Ensure your transcript files are in supported formats (PDF, TXT, DOCX) and are not corrupted.

### Getting Help

If you encounter any issues:
1. Check that both servers are running (API on port 5000, frontend on port 5173)
2. Verify all dependencies are installed
3. Check the browser console for JavaScript errors
4. Check the Flask API console for Python errors

## Development

### Frontend Development
The React application uses:
- TypeScript for type safety
- Tailwind CSS for styling
- Lucide React for icons
- Vite for build tooling

### Backend Development
The Python analysis engine uses:
- Flask for the API server
- Pandas and NumPy for data processing
- PyPDF2 and python-docx for file parsing
- Matplotlib and Seaborn for visualization

## License

This project is for educational and demonstration purposes.
