Chat Bot: नमस्ते,मैं <PERSON><PERSON><PERSON> बोल रही हूँ aditya birla health insurance से, क्या मैं mukesh से बात कर रही हूँ?
Human: Hello hello
Human: Hello.
Chat <PERSON>t: Hello, I am <PERSON><PERSON><PERSON> from Aditya Birla Health Insurance.
Chat Bot: Please note this call is being recorded for quality and monitoring purposes.
Chat Bot: Hope that's fine?
Human: बोलिए बोलिए.
Chat Bot: हमें आपकी request Aditya Birla Health Insurance website से मिली है.
Chat Bot: तो मैं बस यह check करना चाहती थी कि क्या आप fresh policy की तलाश में हैं, या अपनी existing policy को port करने के बारे में सोच रहे हैं?
Human: Quality की class में है
Chat Bot: ठीक है, तो आप एक नई health insurance policy लेना चाहते हैं, correct?
Human: Yes.
Chat Bot: Great, and to proceed further, may I know your age?
Human: Twenty twenty four
Chat Bot: Thank you for sharing that.
Chat <PERSON><PERSON>: Just to confirm, is Four Eight Four Six Six One the correct pincode for your city?
Human: हां हां. Four four eight अड़तालीस चवालीस इकसठ है
Chat Bot: अच्छा, क्या आप कोई regular physical activity करते हैं, जैसे walking, yoga, gym, या कुछ similar?
Human: जी.
Chat Bot: क्या आपकी कोई pre-existing health conditions हैं, जैसे asthma, diabetes, blood pressure, heart issues, या कुछ और जिसके बारे में हमें जानना चाहिए?
Human: No no no कुछ.
Chat Bot: <silence>
Chat Bot: क्या आप currently किसी health condition के
Human: कुछ भी नहीं है.
Chat Bot: <silence>
Chat Bot: अच्छा, क्या आप कभी अस्पताल में भर्ती हुए हैं?
Chat Bot: Hello?
Human: No.
Chat Bot: क्या किसी doctor ने recently किसी medical condition के लिए surgery या treatment recommend किया है?
Chat Bot: Hello?
Human: नहीं नहीं नहीं.
Human: नहीं नहीं.
Chat Bot: ## Step 15: Policy Suggestion
Chat Bot: समझ गई, धन्यवाद जानकारी के लिए.
Chat Bot: Based on the details you've shared, मैं "Activ Fit" policy की सलाह दूंगी.
Chat Bot: यह policy specially young और healthy individuals के लिए designed है, जो कम premium में ज्यादा rewards चाहते हैं.
Chat Bot: क्या आप इसके बारे में अधिक जानने में interested हैं?
Chat Bot: Hello?
Human: Yes yes बोलिए.
Human: हां हां बोलिए ना.
Chat Bot: If you choose the Activ Fit policy, the initial waiting period will be thirty
Chat Bot: <silence>
Chat Bot: अगर आप "Activ Fit" policy लेते हैं, तो इसका initial waiting period thirty days का होगा, pre-existing diseases का waiting period three years का होगा, और specific diseases का waiting period two years का होगा.
Chat Bot: क्या ये information आपकी help कर रही है?
Human: हां.
Chat Bot: अच्छा, मैं आपको Activ Fit के
Human: Ok ok ठीक है.
Chat Bot: <silence>
Chat Bot: हमारे discussion के basis पर, इस policy में features हैं जैसे Health Returns, Claim Protect, Super Reload, और Binge Refill.
Chat Bot: क्या आप अब application के साथ आगे बढ़ना चाहेंगे?
Chat Bot: Hello?
Human: हां बोलिए बोलिए.
Chat Bot: Great, हमारे पास आपका email address है sahumukesheight
Human: हां बोलिए ना बोलिए ना.
Chat Bot: <silence>
Chat Bot: हमारे पास आपका email address है sahumukesheight eight one seven at the rate gmail dot com, क्या यह correct है?
Human: Ok ok.
Chat Bot:
Human: हां
Chat Bot: जैसे ही आप insurance quotation जो हमने भेजा है उसे review कर लेंगे, मैं आपके convenience के अनुसार एक callback schedule कर दूंगी ताकि हम next steps discuss कर सकें.
Chat Bot: आप एक convenient समय बताएँ ताकि मैं callback schedule कर सकूं?
Human: Ok ok.
Chat Bot: क्या यह सही रहेगा अगर मैं आपको कल दो बजे call करूं, या आप सुबह ग्यारह से शाम छह बजे के बीच कोई और time prefer करेंगे?
Human: नहीं सुबह से करना sir सुबह से खाली रहेगा
Human: Ok.
Chat Bot: Are you there?
Chat Bot: कोई problem नहीं.
Chat Bot: सुबह ग्यारह से शाम छह के बीच कौन सा समय आपके लिए सबसे convenient रहेगा?
Human: Ya lo voy checar.
Chat Bot: Lo siento, pero actualmente solo puedo asistir en inglés o hindi.
Chat Bot: ¿Le gustaría que organice una llamada de un asesor específico para su idioma?
Chat Bot: "
Chat Bot: Hello?
Chat Bot: Hello?
Human: हां हां बोलिए.
Chat Bot: Great! मैं कल दोपहर Gyaarah Baje पर callback schedule कर रही हूँ. अगर आपके और कोई
Human: हां हां बोलिए ना.
Chat Bot: <silence>
Chat Bot: आपका time के लिए धन्यवाद.
Chat Bot: मैं scheduled time पर follow up करूंगी.
Chat Bot: अगर आपके पास और कोई questions हैं, तो हमें संपर्क करें.
Chat Bot: Have a great day. Goodbye.

---

Chat Bot: नमस्ते,मैं Rahul  बोल रहा  हूँ aditya birla health insurance से, क्या मैं Swayam से बात कर रहा हूँ?
Human:  Yes. This is Sam.
Chat Bot: Thank you, Sam.
Chat Bot: Please note इस call को quality और monitoring purpose के लिए record किया जा रहा है.
Chat Bot: Hope that's fine?
Human:  Yeah. That's fine.
Chat Bot: Actually हमें आपकी request Aditya Birla Health Insurance website से मिली है.
Chat Bot: तो मैं बस यह check करना चाहता था कि क्या आप fresh policy की तलाश में हैं, या अपनी existing policy को port करने के बारे में सोच रहे हैं?
Human:  No. I don't have a policy as of now.
Chat Bot: अच्छा, तो आप नई health insurance policy लेना चाहते हैं। आपको better guide करने के लिए - क्या आप सिर्फ अपने लिए coverage चाहते हैं, या family members को भी include करना चाहते हैं?
Human:  No. So I wanted to understand, like, doesn't this really work after I grow to a certain age like today I have no health condition but later on I may need some cover. So I just wanted to understand why I need health insurance.
Chat Bot: Health insurance आपके लिए एक financial safety net की तरह काम करता है, जो आपको unexpected medical expenses से protect करता है.
Chat Bot: आज भले ही आपको कोई health condition न हो, लेकिन future में health risks बढ़ सकते हैं जैसे age increase होने के साथ, और कभी-कभी अचानक hospitalization या medical treatment की जरूरत भी पड़ सकती है, जो काफी महंगी हो सकती है.
Chat Bot: Health insurance की वजह से न सिर्फ आप financial burdens से बचे रह सकते हैं, बल्कि आप time पर और बिना चिंता किए आवश्यक medical care भी ले सकते हैं.
Chat Bot: इसके साथ ही, कुछ policies health management benefits भी offer करती हैं जैसे annual health checkups, जिसके तहत आप अपनी health का regular checkup maintain कर सकते हैं.
Chat Bot: बीमारी या medical emergencies कभी भी आ सकती हैं.
Chat Bot: Health insurance आपके लिए एक financial safety net की तरह काम करता है, जो आपको
Human:  So how much will it so how much will you charge me with?
Chat Bot: Great, आगे बढ़ने के लिए  आपकी कुछ details चाहिए होंगी जिससे कि हम आपकी specific needs के अनुसार सही policy recommend कर सकें। क्या मैं आपकी age जान सकता हूँ?
Human:  No. I shared my age, but, like, what if I change job or I'm changing locations? Would my policy still old?
Chat Bot: Yes, absolutely. Once you have a health insurance policy, it remains valid even if you change jobs or relocate. Your policy is with you regardless of your employment status or location. You just need to make sure to inform us about your new address or any other contact details so that your records stay updated for any communication. Do you have any other questions or would you like to proceed with exploring policy
Human:  So let's say I make the payment today, you will not ask me for money again next year or the year after that, right? Like once I make a health insurance payment, it there. Like like like a life insurance.
Chat Bot: <silence>
Chat Bot: Health insurance का purpose different होता है.
Chat Bot: Health insurance एक annual contract की तरह होता है, जिसका मतलब है कि आपको हर साल policy renew करनी होगी और इसके लिए annual premium pay करना होगा.
Chat Bot: Health insurance life insurance की तरह one-time payment या whole life coverage की तरह नहीं होता है.
Chat Bot: Annual renewal की process से policyholder की needs, premium, और coverages को साल दर साल adjust किया जा सकता है.
Chat Bot: इस तरीके से, आप अपनी policy में बदलाव भी कर सकते हैं, जैसे coverage amount adjust करना या add-on benefits लेना.
Chat Bot: अगर आप ज्यादा details में जानना चाहते हैं या एक specific quote देखना चाहते हैं, तो मैं आपकी age और कुछ basic information collect करके आपको best options बता सकता हूं.
Human:  No. So, like, can I sort of make a payment today? But, you know, because I don't have a condition, can I pause it and then restart it, let's say, one or two years later?
Chat Bot: Health insurance policies generally can't be paused and restarted like that.
Chat Bot: They need to be maintained continuously through regular premium payments.
Chat Bot: If you skip a renewal, the policy lapses, and you lose all accumulated benefits, like waiting period credit and coverage continuity.
Chat Bot: If you choose to get a policy again later, you'd need to reapply and go through the waiting periods anew.
Chat Bot: It's often more beneficial to keep your policy active consistently to maintain continuous coverage, especially since health issues can arise unexpectedly.
Chat Bot: Do you have any other questions about health insurance policies or coverage?
Human:  So can I add my partner, like, after I get married?
Chat Bot: Yes, absolutely! After you get married, you can definitely add your partner to your existing health insurance policy.
Chat Bot: This typically involves notifying your insurance provider about the change in your family status and submitting any required documents related to your marriage.
Chat Bot: It's a good way to ensure that both you and your partner have comprehensive health coverage under one plan.
Chat Bot: If you need help with this process or have any more questions, feel free to ask!
Human:  So, like, today, have bought a plan and, you know, there's some waiting period that I've already, you know, sort of waited for. And then I get married. So do that applies to my partner as well. Like, she will not have to then go through the waiting period again.
Chat Bot: When you add your partner to your existing health insurance policy, she would typically have her own waiting periods that would need to be completed, even if you have already completed yours.
Chat Bot: The waiting periods apply individually to each policy member.
Chat Bot: However, some insurance providers might offer certain terms or discounts based on your existing coverage, so it's always good to check the specific terms with your insurance provider.
Chat Bot: If you have any questions or need further assistance on this, feel free to ask.
Human:  So who is this insurance provider who can give me this?
Chat Bot: मैं सिर्फ Aditya Birla Health Insurance का AI representative हूं.
Chat Bot: क्या आपको Health Insurance से जुड़ा कोई doubt है?
Human:  So which insurance provider will allow give me this discount?
Human:  Tata दे देगा यह?
Chat Bot: मैं सिर्फ Aditya Birla Health Insurance का representative हूं.
Chat Bot: क्या आपको Aditya Birla Health Insurance के बारे में कोई doubt है?
Human:  Link with our Thank you.
Human:  So much.
Human:  Bye.
Chat Bot: Goodbye.

---

Chat Bot: नमस्ते,मैं Rahul  बोल रहा  हूँ aditya birla health insurance से, क्या मैं Ibrahim से बात कर रहा हूँ?
Human:  हां जी बोलिए.
Chat Bot: धन्यवाद. कृपया ध्यान दें कि यह कॉल quality और monitoring purpose के लिए record की जा रही है.
Chat Bot: आशा करता हूँ कि आपको यह ठीक लगेगा?
Chat Bot: Are you there?
Human:  Person you are speaking
Chat Bot: Actually हमें आपकी request Aditya Birla Health Insurance website से मिली है.
Chat Bot: तो मैं यह check करना चाहता था कि क्या आप fresh policy की तलाश में हैं, या अपनी existing policy को port करने के बारे में सोच रहे हैं?
Human:  fresh policy के लिए.
Chat Bot: अच्छा, तो आप नई health insurance policy लेना चाहते हैं.
Chat Bot: आपको better guide करने के लिए - क्या आप सिर्फ अपने लिए coverage चाहते हैं, या family members को भी include करना चाहते हैं?
Human:  Family members will be
Chat Bot: Perfect, बस एक बात confirm कर दीजिए - आप कितने family members को policy में include करना चाहते हैं?
Human:  सिर्फ एक, me and my wife.
Chat Bot: आपकी और आपकी wife की age क्या है?
Human:  Mary Chovan sal.
Chat Bot: धन्यवाद! मेरे record के अनुसार आपका pincode six zero zero zero one four है, क्या यह सही है?
Human:  हां.
Chat Bot: धन्यवाद, आगे बढ़ते हैं। क्या आप या आपकी wife कोई regular physical activity करते हैं जैसे walking, yoga, gym?
Human:  नहीं नहीं नहीं generally sir walking.
Chat Bot:
Human: Walking and सिर्फ generally walking करते हैं हम.
Human:  हां diabetes diabetes है.
Chat Bot: समझ गया, यह जानकर अच्छा लगा! क्या आपकी या आपकी wife की कोई pre-existing health conditions हैं जैसे asthma, diabetes, blood pressure, heart issues, या
Chat Bot: <silence>
Chat Bot: मुझे यह सुनकर खेद है कि आपकी wife को diabetes है.
Chat Bot: हम आपकी सहायता के लिए उचित नीति खोजने का प्रयास करेंगे जो आपके स्वास्थ्य की ज़रूरतों को पूरा करे.
Chat Bot: क्या आप currently किसी health condition के लिए कोई medications ले रहे हैं?
Human:  Diabetes or cholesterol.
Chat Bot: समझ गया, आपकी स्थिति के लिए हम बहुत समझ रखते हैं। क्या आप या आपकी wife recently hospitalized हुए हैं या क्या किसी doctor ने recently किसी medical condition के लिए surgery या treatment recommend किया है?
Human:  नहीं नहीं, January में मैं hospitalized हुआ था.
Human:  Stone के लिए.
Human:  Removal of kidney stone के लिए.
Chat Bot: मुझे खेद है कि आपको यह अनुभव करना पड़ा। हम आपकी स्थिति को बेहतर ढंग से समर्थन करने के लिए यहाँ हैं। आपकी जानकारी के आधार पर, मैं आपको Activ One V-Y-T-L policy की सिफारिश कर सकता हूँ। यह policy Day one से ही chronic diseases, जैसे diabetes, cover करती है और chronic management program भी प्रदान करती है। क्या आप इसके बारे में और जानना चाहेंगे?
Human:  हम बताइए.
Chat Bot: Activ One VYTL policy पूरी तरह से peace of mind के लिए विशेष रूप से design की गई है, खासकर उन लोगों के लिए जिनको chronic problems हैं या होने का chance है.
Chat Bot: इसमें  Day one से ही chronic diseases जैसे कि diabetes, BP, asthma, और अन्य conditions बिना किसी waiting period के cover होती है.
Chat Bot: इसमें chronic management program भी शामिल है जिसमें personal health coach, specialist consultation, और जरूरी tests शामिल हैं आपकी chronic illness को manage करने के लिए.
Chat Bot: इसके अलावा, consumables cover किए जाते हैं, और आप up to hundred percent health returns भी earn कर सकते हैं जो अगले साल के renewal premium को कम करता है.
Chat Bot: क्या आप इसके साथ आगे बढ़ना चाहेंगे या इसमें कोई और जानकारी चाहिए?
Chat Bot: Hello?
Human:  बताइए बताइए.
Chat Bot: Activ One VYTL policy के मुख्य फायदे हैं:
Chat Bot: - Day One से ही chronic diseases cover करती है जैसे कि आपकी diabetes और अन्य conditions.
Chat Bot: - इसमें chronic management program मिलता है जिसमें personal health coach और specialist consultations शामिल हैं.
Chat Bot: - Consumables जैसे gloves और PPE covered होते हैं.
Chat Bot: - Up to hundred percent health returns जो अगले साल के renewal premium को कम करते हैं.
Chat Bot: इस policy के साथ proceed करने के लिए क्या मैं आपके registered email address पर quotation भेज सकता हूँ?
Chat Bot: Hello?
Human:  हां, quotation भेजी है.
Human:  Existing हां.
Chat Bot: Great! हमारे पास आपका email address
Human:  PED के लिए print
Chat Bot: <silence>
Human:  waiting period क्या है?
Chat Bot: Activ One VYTL policy के तहत pre-existing diseases के लिए waiting period तीन साल है। लेकिन खास बात यह है कि chronic diseases जैसे diabetes Day One से ही cover होती हैं। क्या मैं आपके लिए कुछ और assist कर सकता हूँ?
Human:  उसके लिए reduction नहीं हो सकता.
Human:  तीन साल से.
Human:  कम
Chat Bot: खेद है, लेकिन Activ One VYTL policy में pre-existing diseases के लिए जो standard waiting period है, उसे reduce करने का कोई option नहीं है। यह standard policy term का हिस्सा है। आपका कोई और सवाल है तो बताएं, मैं आपकी मदद करने के लिए यहाँ हूँ।
Chat Bot: Hello?
Human:  Hello? 

---

Chat Bot: नमस्ते, मैं Rahul बोल रहा हूँ aditya birla health insurance से, क्या मैं Chetan Thakur से बात कर रहा हूँ?
Human: हां जी.
Chat Bot:
Human: हां जी.
Chat Bot: Great,Thank you. Please note यह Call quality और monitoring purpose के लिए record की जा रही है.
Chat Bot: Hope that's fine?
Human: बताइए हां.
Chat Bot: Actually हमें आपकी request Aditya Birla Health Insurance website से मिली है.
Chat Bot: तो मैं बस यह check करना चाहता था कि क्या आप fresh policy की तलाश में हैं, या अपनी existing policy को port करने के बारे में सोच रहे हैं?
Human: Main pastilla.
Human: पैसे देख रहा था.
Chat Bot: अच्छा, तो आप नई health insurance policy लेना चाहते हैं, आपको better guide करने के लिए - क्या आप सिर्फ अपने लिए coverage चाहते हैं, या family members को भी include करना चाहते हैं?
Human: Family है.
Chat Bot: Perfect, बस एक बात confirm कर दीजिए - आप कितने family members को policy में include करना चाहते हैं?
Human: मैं हूं मेरी wife और एक बच्चा है.
Chat Bot: Great, क्या आप अपने सभी family members की ages बता सकते हैं?
Human: मेरे उनतीस साल है, wife की छब्बीस है.
Human: और बच्चा भी पांच महीने का है.
Chat Bot: धन्यवाद, Confirm करने के लिए, क्या two zero one three zero one आप के city का सही pincode है?
Human: Two zero हां यही है.
Chat Bot: Perfect! Main अब next step पर proceed करता हूं.
Chat Bot: अच्छा, क्या आप या आपके family members कोई regular exercise या wellness routine follow करते हैं - जैसे yoga, walking, या कुछ और?
Human: हमारा जैसे घर का ही काम है जो खेती बाड़ी है तो इस ही नहीं पड़ती.
Chat Bot: समझ गया. क्या आपकी या आपके family members की कोई pre-existing health conditions हैं जैसे asthma, diabetes, BP, heart problems, या कुछ और
Human: नहीं कुछ नहीं सब fit है. सब fit है. सब fit है.
Chat Bot: <silence>
Chat Bot: अच्छा
Human: कुछ भी नहीं अभी तो फ़िलहाल सब fit है.
Chat Bot: <silence>
Chat Bot: Great, और आगे बढ़ने के लिए-
Human: मैं कैसे बात कर रहा हूं normal बात कर रहा या लग रहा sir computer से वापस सुबह 

---

Chat Bot: नमस्ते,मैं Rahul बोल रहा हूँ aditya birla health insurance से, क्या मैं Haradhan से बात कर रहा हूँ?
Human: हां हां बोलिए ना.
Chat Bot: Are you there?
Human: Hello.
Human: हां बोलिए ना?
Human: Hello.
Chat Bot: Great,Thank you. Please note यह Call quality और monitoring purpose के लिए record की जा रही है.
Chat Bot: Hope that's fine?
Chat Bot: Are you there?
Human: हां बोलिए ना.
Human: बोलिए?
Chat Bot: Actually हमें आपकी request Aditya Birla Health Insurance website से मिली है.
Chat Bot: तो मैं बस यह check करना चाहता था कि क्या आप fresh policy की तलाश में हैं, या अपनी existing policy को port करने के बारे में सोच रहे हैं?
Human: Actually क्या है ना मैं BYJU'S PAN में उसका उससे लिया था policy.
Chat Bot: ओके, आप अपनी current health insurance
Human: लेकिन उन लोगों को sir कोई
Chat Bot: <silence>
Chat Bot: ओके, आप अपनी current health insurance policy को port करना चाहते हैं, हम definitely इसमें आपकी help कर सकते हैं.
Chat Bot: क्या आप please अपने current insurer का नाम और कुछ policy details share कर सकते हैं?
Human: क्या बोल रहा हूं?
Human: मैं budget finance के ऊपर से लिया था policy.
Human: नहीं दे रहा है?
Chat Bot: अच्छा, share करने के लिए धन्यवाद.
Chat Bot: Actually हमें बहुत खेद है, लेकिन porting तभी possible है जब policy आज से two से forty five days के बीच expire हो रही हो.
Chat Bot: लेकिन हाँ, आप fresh policy options explore कर सकते हैं.
Chat Bot: क्या आप उन्हें check करने में interested होंगे?

---

Chat Bot: नमस्ते,मैं Rahul बोल रहा हूँ aditya birla health insurance से, क्या मैं Rajiv से बात कर रहा हूँ?
Human: जी.
Human: बोलिए.
Chat Bot: Great,Thank you. Please note यह Call quality और monitoring purpose के लिए record की जा रही है.
Chat Bot: Hope that's fine?
Human: Yeah.
Chat Bot: Actually हमें आपकी request Aditya Birla Health Insurance website से मिली है.
Chat Bot: तो मैं बस यह check करना चाहता था कि क्या आप fresh policy की तलाश में हैं, या अपनी existing policy को port करने के बारे में सोच रहे हैं?
Chat Bot: "
Chat Bot: Are you there?
Human: Port करने का नहीं है.
Human: Top up करने के लिए.
Chat Bot: "क्या आप अपनी existing Aditya Birla Health Insurance coverage को top-up plan के साथ enhance करने में interested होंगे?
Chat Bot: "
Human: जी.
Chat Bot: हमारी team का एक member जल्द ही आपसे reach out करेगा ताकि आपकी existing policy के लिए available top-up options discuss कर सकें.
Chat Bot: आपके time के लिए धन्यवाद, have a great day. Goodbye.

---

Chat Bot: नमस्ते,मैं Ananya बोल रही हूँ aditya birla health insurance से, क्या मैं Saurabh से बात कर रही हूँ?
Human: हां बोलिए.
Chat Bot: Great, Thank you. Please note यह Call
Human: हां हां
Chat Bot: <silence>
Chat Bot: Human: I love नहीं भेजना madam. He is also, she is also for 100,000, right?
Human: आपकी आवाज़ नहीं आ रही मैं आवाज़ नहीं आ रही. Are in खैता हुआ. लेकिन सच कर लेना. I think में थोड़ा लगे
Chat Bot: मुझे अच्छे से समझ नहीं आया, आप फिर से बोल सकते हैं?
Human: उसे बोला. नहीं आप बोलिए वापस क्या बोल रहे हैं sir?
Chat Bot: Actually हमें आपकी request Aditya Birla Health Insurance website से मिली है.
Chat Bot: तो मैं बस यह check करना चाहती थी कि क्या आप fresh policy की तलाश में हैं, या अपनी existing policy को port करने के बारे में सोच रहे हैं?
Human: हां but मुझे diabetes है.
Chat Bot: मैं समझ सकती हूँ कि diabetes manage करना challenging हो सकता है, लेकिन हमारी Activ One Vital policy specially आपके जैसे लोगों के लिए बनाई गई है.
Chat Bot: यह policy complete peace of mind के लिए specially design की गई है.
Chat Bot: Day one से ही chronic diseases covered हैं - Diabetes, BP, Asthma, और seven और conditions बिना किसी waiting period के.
Chat Bot: इसमें Chronic Management Program भी मिलता है जिसमें personal health coach, specialist consultation, और जरूरी tests included हैं आपकी chronic illness को properly manage करने के लिए.
Chat Bot: साथ ही Consumables भी covered हैं, और up to hundred percent Health Returns भी earn कर सकते हैं जो अगले साल के renewal premium को कम करते हैं जिससे renewal almost free हो सकती है.
Chat Bot: क्या मैं इसके benefits के बारे में कुछ और बता सकती हूँ?
Human: नहीं but vital plan बहुत महंगा है उस उसके अलावा कुछ और है.
Chat Bot: अगर आप Activ One Vital के अलावा कुछ और देखना चाहते हैं, तो मैं recommend करूंगी कि आप "Activ One Maxplus" पर भी विचार करें. यह एक premium plan है जिसमें hospitalization
Chat Bot: Hello?
Human: हां बोलिए.
Human: Through
Chat Bot: अगर आप Activ
Human: बोलिए बोलिए बोलिए.
Chat Bot: <silence>
Chat Bot: अगर आप Activ One Vital के अलावा कुछ और चाह रहे हैं, तो मैं recommend करूंगी कि आप "Activ One Maxplus with Chronic Care from Day One" पर भी विचार करें.
Chat Bot: यह Maxplus का enhanced version है जिसमें Day one से ही chronic diseases cover हैं, जैसे कि diabetes, BP, asthma, और heart disease.
Chat Bot: इसे specifically chronic conditions वाले लोगों के लिए design किया गया है.
Chat Bot: इस plan में no caps on hospitalization, five times Super Credit, और consumables coverage जैसी सुविधाएँ भी शामिल हैं.
Chat Bot: क्या आप इसके बारे में और जानकारी चाहते हैं?
Human: नहीं यह plan तो मुझे अच्छा लग रहा है और अगर मुझे diabetes नहीं है और मैं कहूं तो

---

Chat Bot: नमस्ते,मैं Rahul बोल रहा हूँ aditya birla health insurance से, क्या मैं Jagdeesh से बात कर रहा हूँ?
Human: जी.
Human: जी sir.
Human: जी sir.
Chat Bot: Great,Thank you. Please note यह Call quality और monitoring purpose के लिए record की जा रही है। Hope that's fine?
Human: जी.
Chat Bot: Actually हमें आपकी request Aditya Birla Health Insurance website से मिली है.
Chat Bot: तो मैं बस यह check करना चाहता था कि क्या आप fresh policy की तलाश में हैं, या अपनी existing policy को port करने के बारे में सोच रहे हैं?
Chat Bot: Hello?
Human: Sir मैं समझ नहीं पा रहा हूं sir?
Chat Bot: मुझे अच्छे से समझ नहीं आया, आप फिर से बोल सकते हैं?
Chat Bot: क्या आप एक नई policy लेना चाहते हैं, अपनी मौजूदा policy को port करना चाहते हैं, या आप पहले से ही Aditya Birla Health Insurance के ग्राहक हैं और अपनी मौजूदा policy में कुछ बदलाव चाहते हैं?
Human: New policy देना चाहते
Chat Bot: ठीक है, तो आप एक नई health insurance policy लेना चाहते हैं, correct?
Human: I do.
Chat Bot: Great, तो आप नई health insurance policy लेना चाहते हैं.
Chat Bot: आपको better guide करने के लिए - क्या आप सिर्फ अपने लिए coverage चाहते हैं, या family members को भी include करना चाहते हैं?
Chat Bot: Hello?
Human: अपने लिए
Chat Bot: Great, और आगे बढ़ने के लिए, क्या मैं आपकी age जान सकता हूँ?
Human: Edge.
Human: Bison.
Chat Bot: मुझे अच्छे से समझ नहीं आया.
Chat Bot: आप फिर से बोल सकते हैं कि आपकी उम्र क्या है?
Chat Bot: "
Human: 22.
Chat Bot: धन्यवाद. Confirm करने के लिए, क्या four eight one eight eight four आप के city का सही pincode है?
Human: जी.
Chat Bot: अच्छा, क्या आप कोई regular physical activity करते हैं जैसे walking, yoga, gym, या कुछ similar?
Human: Okay. Builder.
Chat Bot: मुझे अच्छे से समझ नहीं आया.
Chat Bot: आप फिर से बता सकते हैं कि आप किसी नियमित physical activity जैसे walking, yoga, या gym करते हैं?
Human: Working.
Chat Bot: अच्छा, क्या आपकी कोई pre-existing health conditions हैं जैसे asthma, diabetes, blood pressure, heart issues, या कुछ और जिसके बारे में हमें जानना चाहिए?
Human: Debitis.
Chat Bot: समझ सकता हूँ, मैं आपकी situation को appreciate करता हूँ.
Chat Bot: क्या आप currently किसी health condition के लिए कोई medications ले रहे हैं?
Human: जी.
Chat Bot: कोई problem नहीं.
Chat Bot: यह बस हमें उन plans को recommend करने में help करता है जो आपकी health situation के साथ best fit करते हैं.
Chat Bot: आपकी privacy हमेशा respected और protected रहती है.
Chat Bot: क्या आप कभी अस्पताल में भर्ती हुए हैं?
Human: जी sir.
Chat Bot: मैं समझ सकता हूँ.
Chat Bot: यह बस यह ensure करने के लिए है कि हम आपके लिए best plans recommend कर सकें.
Chat Bot: क्या किसी doctor ने recently किसी medical condition के लिए surgery या treatment recommend किया है?
Human: No.
Chat Bot: Thank you for sharing these details.
Chat Bot: Based on the information you provided, I would recommend the "Activ One Vital" policy for you.
Chat Bot: It's specially designed to provide peace of mind for individuals with chronic conditions or the potential for chronic issues.
Chat Bot: Would you like to know more about this policy, or would you like me to provide additional options?
Human: Thank you.
Chat Bot: The "Activ One Vital" policy is designed to give you complete peace

---

Chat Bot: नमस्ते,मैं Rahul  बोल रहा  हूँ aditya birla health insurance से, क्या मैं Bharatkumar  से बात कर रहा हूँ?
Human:  Blue.
Chat Bot: Great,Thank you. Please note यह Call quality और monitoring purpose के लिए record की जा रही है.
Chat Bot: Hope that’s fine?
Human:  Got?
Chat Bot: यह Call quality और monitoring purpose के लिए record की जा रही है.
Chat Bot: Hope that’s fine?
Human:  Okay. Fine.
Chat Bot: Actually हमें आपकी request Aditya Birla Health Insurance website से मिली है.
Chat Bot: तो मैं बस यह check करना चाहता था कि क्या आप fresh policy की तलाश में हैं, या अपनी existing policy को port करने के बारे में सोच रहे हैं?
Human:  हिंदी में कोई बोलेंगे.
Chat Bot: 
Human:  Hello?
Chat Bot: Actually हमें आपकी request Aditya Birla Health Insurance website से मिली है.
Chat Bot: तो मैं बस यह check करना चाहता था कि क्या आप fresh policy की तलाश में हैं, या अपनी existing policy को port करने के बारे में सोच रहे हैं?
Human:  Policy तो थी मेरे पास पर अभी नहीं है खत्म हो गई. दुबारा लेने की है.
Chat Bot: ठीक है, तो आप एक नई health insurance policy
Human:  इसलिए से बात कर रहा है.
Chat Bot: <silence>
Chat Bot: अच्छा,  तो आप नई health insurance policy लेना चाहते हैं, आपको better guide करने के लिए - क्या आप सिर्फ अपने लिए coverage चाहते हैं, या family members को भी include करना चाहते हैं?
Human:  नाम दो है मैं मेरी wife.
Chat Bot: Perfect, बस एक बात confirm कर दीजिए - आप कितने family members को policy में include करना चाहते हैं?
Human:  Hello?
Chat Bot: समझ गया, क्या आप अपने सभी family members की ages बता सकते हैं?
Human:  Hello?
Chat Bot: मुझे अच्छे से समझ नहीं आया, आप फिर से बोल सकते हैं?
Human:  Ma'am दो ही है फिर सभी के क्या हां
Chat Bot: ठीक है,
Human:  अच्छा daughter पता था daughter में चाहिए मुझे policy.
Chat Bot: <silence>
Human:  Policy मैं और मेरी wife दो दोनों हैं.
Chat Bot: समझ गया, आपको और आपकी
Human:  मेरी age मेरी age है 72.
Chat Bot: <silence>
Chat Bot: धन्यवाद, और आपकी wife की age क्या है?
Human:  और wife की है 68.
Chat Bot: धन्यवाद, आपकी और आपकी wife की ages मिल गई हैं.
Chat Bot: अब कृपया confirm करिए कि क्या three six two seven two zero आपके city का सही pincode है?
Human:  Three six two
Chat Bot: Sorry, मुझसे mistake हुई है. आप का city का correct
Human:  two six two eight two four.
Chat Bot: <silence>
Chat Bot: धन्यवाद, आपने पिनकोड two six two eight
Human:  Your code is our good.
Chat Bot: <silence>
Chat Bot: मुझे अच्छे से समझ नहीं आया, आप फिर से बोल सकते हैं?
Human:  यह code in our का number है, code in our गुजरात में गिर from नाजिश्ते.
Chat Bot: Sorry, मुझसे mistake हुई है.
Chat Bot: आप का city का correct pincode क्या है?
Chat Bot: Main इसे अपने records में update कर दूंगा.
Human:  मुझे मालूम नहीं है अभी क्या है?
Chat Bot: कोई बात नहीं, हम आगे बढ़ते हैं. समझ गया. आपके और
Human:  Three six two seven two zero.
Chat Bot: Are you there?
Human:  PIN code number.
Chat Bot: धन्यवाद, confirm करने के लिए. Three six two seven two zero आपके city का सही pincode है. अब, क्या आप या आपके family members
Human:  Three six two three six two seven two zero.
Chat Bot: 
Human:  Hello?
Chat Bot: मुझे अच्छे से समझ नहीं आया, आप फिर से बोल सकते हैं?
Human:  Check करते हो जाता है बार बार.
Chat Bot: समझ गया.अब, क्या आप या आपकी
Human:  नहीं मैं आपका phone बार बार cut क्यों हो जाता है?
Chat Bot: <silence>
Chat Bot: मुझे खेद है अगर आपको कोई असुविधा हो रही है.
Chat Bot: मैं यहाँ हूँ आपकी सहायता करने के लिए.
Chat Bot: क्या आप या आपके family members कोई regular exercise या wellness routine follow करते हैं - जैसे yoga, walking, या कुछ और?
Human:  हां, working तो कर दे दूसरी थोड़ी थोड़ी बहुत बहुत कुछ नहीं exercise भी करते हैं.
Chat Bot: समझ गया. क्या आपकी या आपके family members की कोई pre-existing health conditions हैं जैसे asthma, diabetes, BP, heart problems, या कुछ और जिसके बारे में हमें aware होना चाहिए?
Human:  हां है ना answer है.
Chat Bot: धन्यवाद जानकारी के लिए.-
Human:  मिल सकती है कोई policy यहां है.
Chat Bot: मैं समझ सकता हूँ कि आपके पास pre-existing conditions हैं। इस स्थिति के लिए हम "Activ One V-Y-T-L" policy की सलाह देते हैं। इस policy में Day one से ही chronic
Chat Bot: Hello?
Human:  निकाल दो अपने direct
Chat Bot: अच्छा, मैं आपको Activ One V-Y-T-L के बारे में बताता हूं.
Chat Bot: यह policy complete peace of mind के लिए specially design की गई है, especially उन लोगों के लिए जिनको chronic problems हैं या होने का chance है.
Chat Bot: Day one से ही chronic diseases covered हैं - Diabetes, BP, Asthma, और seven और -
Chat Bot: Are you there?
Human:  वह तो नहीं है, diabetes है. हां diabetes diabetes है.
Chat Bot: समझ गया. मैं आपको Activ One V-Y-T-L policy की सलाह देता हूँ.
Chat Bot: इस policy में day one से ही chronic diseases cover हैं, जैसे diabetes, BP, asthma, आदि.
Chat Bot: इसके साथ आपको Chronic Management Program भी मिलता है जिसमें personal health coach, specialist consultation, और जरूरी tests included हैं आपकी chronic illness को properly manage करने के लिए.
Chat Bot: साथ ही consumables भी covered हैं, और आपको up to hundred percent health returns भी मिल सकते हैं जो अगले साल के renewal premium को कम करते हैं.
Chat Bot: Are you there?
Chat Bot: Hello?
Human:  कितना कम कर सकते हैं, कैसे कर सकते हैं?
Chat Bot: हर साल के renewal पर up to hundred
Human:  आप हिंदी में बोलते हो पर मेरी बात समझता हो नहीं रही आपको.
Chat Bot: <silence>
Human:  Cut हो जाता है आपका.
Chat Bot: मैं समझता हूँ, आपको Activ One Vital policy की
Chat Bot: <silence>
Chat Bot: मुझे खेद है अगर आपको कोई असुविधा हो रही
Human:  आप number दोगे तो मुझे थोड़ा policy का number मालूम है.
Chat Bot: <silence>
Chat Bot: आपको Activ One V-Y-T-L policy
Human:  मुझे आपकी policy का कोई भी ना
Chat Bot: <silence>
Human:  अब यहां का आपका यहां agent है
Chat Bot: मुझे खेद है कि मैं आपकी सहायता नहीं कर सका.
Chat Bot: हमारी team से कोई expert आपके संपर्क में जल्द ही संपर्क करेगा.
Chat Bot: आपके समय के लिए धन्यवाद.
Human:  One two.
Chat Bot: मुझे
Human:  हां बस गुजरात में हुआ ना जो उसको बोलो गुजरात में सबराष्ट्र में को जो कोई भी आपका आपका agent होना है उसको बोलिए number contact करे.
Chat Bot: <silence>
Chat Bot: मैं आपकी जानकारी हमारी team को forward कर दूंगा ताकि वे आपके गुजरात में किसी local agent के माध्यम से संपर्क कर सकें.
Chat Bot: क्या यह okay रहेगा?
Human:  हां हां बस ठीक है यह होकर रहेगा हां.
Chat Bot: Thank you for your-

---

Chat Bot: नमस्ते,मैं Kavya बोल रही हूँ aditya birla health insurance से, क्या मैं aasu से बात कर रही हूँ?
Human: हां जी.
Chat Bot: Great, Thank you.
Chat Bot: Please note यह Call quality और monitoring purpose के लिए record की जा रही है.
Chat Bot: Hope that’s fine?
Chat Bot: ”
Human: Ok.
Chat Bot: "Actually हमें आपकी request Aditya Birla Health Insurance website से मिली है.
Chat Bot: तो मैं बस यह check करना चाहती थी कि क्या आप fresh policy की तलाश में हैं, या अपनी existing policy को port करने के बारे में सोच रहे हैं?
Chat Bot: "
Human: नहीं मैं ऐसा कुछ नहीं करूंगा.
Chat Bot: "मुझे अच्छे से समझ नहीं आया कि आप किस तरह की मदद चाहते हैं.
Chat Bot: क्या आप एक नई policy लेना चाहते हैं, अपनी मौजूदा policy को port करना चाहते हैं, या आप पहले से ही Aditya Birla Health Insurance के ग्राहक हैं और अपनी मौजूदा policy में कुछ बदलाव चाहते हैं?
Chat Bot: "
Human: मैं ऐसा कुछ नहीं चाह रहा हूं. मुझे ऐसा नहीं चाहिए health आप देख क्या क्या बोल रहे मुझे समझ नहीं आ रहा है. मैंने यह सब कोई form भी apply था जो आप बोल रहे हैं health health care क्या बोल रहे हैं आप वही सब?
Chat Bot: "ऐसा लग रहा है कि मुझे आपकी request समझने में थोड़ी मुश्किल हो रही है. क्या आप चाहेंगे कि मैं
Human: आपकी आवाज़ फंस रही है. साहब नहीं आ रहा आपकी आवाज़.
Chat Bot: <silence>
Chat Bot: लगता है कि आवाज़ की कुछ problem हो रही है.
Chat Bot: मैं आपके लिए एक callback schedule कर सकती हूँ.
Chat Bot: क्या कल या अगले किसी दिन आपको बात करने का सही समय रहेगा?
Human: अरे ma'am आपकी आवाज़ cut cut
Chat Bot: लगता है कि आवाज़ की कुछ problem हो रही है.
Chat Bot: मैं आपके लिए एक callback schedule कर सकती हूँ.
Chat Bot: क्या कल या अगले किसी दिन आपको बात करने का सही समय रहेगा?
Human: हां मेरे साथ anytime.
Chat Bot: मैं कल दोपहर दो बजे आपको call कर लूंगी या अगर आप किसी और समय को prefer करते हैं, तो बता सकते हैं.
Chat Bot: Please let me know your preference.
Human: पहले ma'am, कल बात सुनिए तो.
Chat Bot: मैं समझ सकती हूँ कि आपने callback के
Human: पहले तो बात सुनिए यार.
Chat Bot: <silence>
Chat Bot: Sorry for the inconvenience.
Chat Bot: Please let me know your preferred time for a callback, and I will ensure someone contacts you at that time.
Human: Ok ok.
Chat Bot:
Human: Ok ma'am
Chat Bot: Goodbye.