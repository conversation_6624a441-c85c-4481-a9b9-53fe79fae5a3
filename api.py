from flask import Flask, request, jsonify
from flask_cors import CORS
import os
import tempfile
from werkzeug.utils import secure_filename
import sys
import json

# Add the src/utils directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src', 'utils'))

from transcriptAnalyzer import AdityaBirlaTranscriptAnalysisSystem

app = Flask(__name__)
CORS(app)  # Enable CORS for all routes

# Configure upload settings
UPLOAD_FOLDER = 'uploads'
ALLOWED_EXTENSIONS = {'pdf', 'txt', 'docx'}

if not os.path.exists(UPLOAD_FOLDER):
    os.makedirs(UPLOAD_FOLDER)

app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

@app.route('/api/analyze', methods=['POST'])
def analyze_transcripts():
    try:
        # Check if files were uploaded
        if 'files' not in request.files:
            return jsonify({'error': 'No files provided'}), 400
        
        files = request.files.getlist('files')
        
        if not files or all(file.filename == '' for file in files):
            return jsonify({'error': 'No files selected'}), 400
        
        # Create a temporary directory for processing
        with tempfile.TemporaryDirectory() as temp_dir:
            uploaded_files = []
            
            # Save uploaded files
            for file in files:
                if file and allowed_file(file.filename):
                    filename = secure_filename(file.filename)
                    filepath = os.path.join(temp_dir, filename)
                    file.save(filepath)
                    uploaded_files.append(filepath)
            
            if not uploaded_files:
                return jsonify({'error': 'No valid files uploaded'}), 400
            
            # Initialize the Aditya Birla analysis system
            analyzer = AdityaBirlaTranscriptAnalysisSystem()
            
            # Analyze all uploaded files
            all_results = []
            for filepath in uploaded_files:
                try:
                    results = analyzer.analyze_transcript_file(filepath)
                    all_results.extend(results)
                except Exception as e:
                    print(f"Error analyzing {filepath}: {e}")
                    continue
            
            if not all_results:
                return jsonify({'error': 'No valid transcripts found in uploaded files'}), 400
            
            # Generate comprehensive report
            report = analyzer.generate_comprehensive_report()
            
            # Convert the report to a format suitable for the frontend
            frontend_data = {
                "totalCalls": report['summary']['total_calls'],
                "successRate": report['summary']['success_rate'],
                "averageAdherence": report['summary']['average_adherence'],
                "calls": [],
                "aggregatedMetrics": {
                    "stepCompletionRates": report['step_analysis']['completion_rates'],
                    "commonPaths": report['flow_analysis']['common_paths'],
                    "policyAccuracy": report['summary']['policy_accuracy']
                }
            }
            
            # Convert individual call results to frontend format
            for result in all_results:
                call_data = {
                    "customerName": result.customer_name,
                    "collectedEntities": result.collected_entities.to_dict(),
                    "policyAnalysis": {
                        "expected": result.policy_analysis.expected,
                        "actual": result.policy_analysis.actual,
                        "match": result.policy_analysis.match,
                        "confidence": result.policy_analysis.confidence
                    },
                    "conversationFlow": {
                        "path": result.conversation_flow.path,
                        "stepNames": result.conversation_flow.step_names,
                        "completionRate": result.conversation_flow.completion_rate,
                        "adherenceScore": result.conversation_flow.adherence_score
                    },
                    "stepStatuses": [
                        {
                            "stepNumber": status.step_number,
                            "stepName": status.step_name,
                            "status": status.status
                        }
                        for status in result.step_statuses
                    ],
                    "outcome": result.outcome
                }
                frontend_data["calls"].append(call_data)
            
            return jsonify(frontend_data)
    
    except Exception as e:
        print(f"Error in analysis: {e}")
        return jsonify({'error': f'Analysis failed: {str(e)}'}), 500

@app.route('/api/analyze', methods=['GET'])
def get_analysis_results():
    """Get existing analysis results"""
    try:
        # Check if we have the transcript file to analyze
        transcript_file = 'aditya_birla_transcripts.txt'
        if not os.path.exists(transcript_file):
            return jsonify({'error': 'No transcript file found'}), 404
        
        # Initialize the Aditya Birla analysis system
        analyzer = AdityaBirlaTranscriptAnalysisSystem()
        
        # Analyze the transcript file
        results = analyzer.analyze_transcript_file(transcript_file)
        
        if not results:
            return jsonify({'error': 'No valid transcripts found'}), 404
        
        # Generate comprehensive report
        report = analyzer.generate_comprehensive_report()
        
        # Convert the report to a format suitable for the frontend
        frontend_data = {
            "totalCalls": report['summary']['total_calls'],
            "successRate": report['summary']['success_rate'],
            "averageAdherence": report['summary']['average_adherence'],
            "calls": [],
            "aggregatedMetrics": {
                "stepCompletionRates": report['step_analysis']['completion_rates'],
                "commonPaths": report['flow_analysis']['common_paths'],
                "policyAccuracy": report['summary']['policy_accuracy']
            },
            "language_analysis": report.get('language_analysis', {}),
            "quality_analysis": report.get('quality_analysis', {})
        }
        
        # Convert individual call results to frontend format
        for result in results:
            call_data = {
                "customerName": result.customer_name,
                "collectedEntities": result.collected_entities.to_dict(),
                "policyAnalysis": {
                    "expected": result.policy_analysis.expected,
                    "actual": result.policy_analysis.actual,
                    "match": result.policy_analysis.match,
                    "confidence": result.policy_analysis.confidence
                },
                "conversationFlow": {
                    "path": result.conversation_flow.path,
                    "stepNames": result.conversation_flow.step_names,
                    "completionRate": result.conversation_flow.completion_rate,
                    "adherenceScore": result.conversation_flow.adherence_score
                },
                "stepStatuses": [
                    {
                        "stepNumber": status.step_number,
                        "stepName": status.step_name,
                        "status": status.status
                    }
                    for status in result.step_statuses
                ],
                "outcome": result.outcome,
                "call_quality": result.call_quality,
                "language": result.language_mix
            }
            frontend_data["calls"].append(call_data)
        
        return jsonify(frontend_data)
    
    except Exception as e:
        print(f"Error getting analysis results: {e}")
        return jsonify({'error': f'Failed to get analysis results: {str(e)}'}), 500

@app.route('/api/upload-transcript', methods=['POST'])
def upload_transcript():
    """Upload a transcript file or text and add it to aditya_birla_transcripts.txt"""
    try:
        transcript_file = 'aditya_birla_transcripts.txt'
        
        # Check if file was uploaded
        if 'file' in request.files:
            file = request.files['file']
            if file and allowed_file(file.filename):
                # Save uploaded file temporarily
                filename = secure_filename(file.filename)
                temp_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
                file.save(temp_path)
                
                # Extract text from the file
                analyzer = AdityaBirlaTranscriptAnalysisSystem()
                if filename.endswith('.pdf'):
                    text = analyzer._extract_pdf_text(temp_path)
                elif filename.endswith('.docx'):
                    text = analyzer._extract_docx_text(temp_path)
                else:  # .txt file
                    with open(temp_path, 'r', encoding='utf-8') as f:
                        text = f.read()
                
                # Clean up temporary file
                os.remove(temp_path)
                
                # Append to transcript file
                with open(transcript_file, 'a', encoding='utf-8') as f:
                    f.write('\n\n---\n\n')
                    f.write(text.strip())
                
                return jsonify({'message': 'Transcript uploaded and added successfully'})
        
        # Check if text was provided
        elif request.is_json and 'transcript_text' in request.json:
            transcript_text = request.json['transcript_text'].strip()
            
            if not transcript_text:
                return jsonify({'error': 'No transcript text provided'}), 400
            
            # Append to transcript file
            with open(transcript_file, 'a', encoding='utf-8') as f:
                f.write('\n\n---\n\n')
                f.write(transcript_text)
            
            return jsonify({'message': 'Transcript text added successfully'})
        
        else:
            return jsonify({'error': 'No file or text provided'}), 400
    
    except Exception as e:
        print(f"Error uploading transcript: {e}")
        return jsonify({'error': f'Upload failed: {str(e)}'}), 500

@app.route('/api/health', methods=['GET'])
def health_check():
    return jsonify({'status': 'healthy', 'message': 'Transcript Analysis API is running'})

if __name__ == '__main__':
    app.run(debug=True, port=5000) 