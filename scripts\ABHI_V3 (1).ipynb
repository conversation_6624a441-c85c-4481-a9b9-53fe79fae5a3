{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Installing spacy...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING: pip is being invoked by an old script wrapper. This will fail in a future version of pip.\n", "Please see https://github.com/pypa/pip/issues/5599 for advice on fixing the underlying issue.\n", "To avoid this problem you can invoke Python with '-m pip' instead of running pip directly.\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Defaulting to user installation because normal site-packages is not writeable\n", "</pre>\n"], "text/plain": ["Defaulting to user installation because normal site-packages is not writeable\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Collecting spacy\n", "</pre>\n"], "text/plain": ["Collecting spacy\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">  Downloading spacy-3.8.7-cp313-cp313-win_amd64.whl.metadata (28 kB)\n", "</pre>\n"], "text/plain": ["  Downloading spacy-3.8.7-cp313-cp313-win_amd64.whl.metadata (28 kB)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Collecting spacy-legacy&lt;3.1.0,&gt;=3.0.11 (from spacy)\n", "</pre>\n"], "text/plain": ["Collecting spacy-legacy<3.1.0,>=3.0.11 (from spacy)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">  Downloading spacy_legacy-3.0.12-py2.py3-none-any.whl.metadata (2.8 kB)\n", "</pre>\n"], "text/plain": ["  Downloading spacy_legacy-3.0.12-py2.py3-none-any.whl.metadata (2.8 kB)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Collecting spacy-loggers&lt;2.0.0,&gt;=1.0.0 (from spacy)\n", "</pre>\n"], "text/plain": ["Collecting spacy-loggers<2.0.0,>=1.0.0 (from spacy)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">  Downloading spacy_loggers-1.0.5-py3-none-any.whl.metadata (23 kB)\n", "</pre>\n"], "text/plain": ["  Downloading spacy_loggers-1.0.5-py3-none-any.whl.metadata (23 kB)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Collecting murmurhash&lt;1.1.0,&gt;=0.28.0 (from spacy)\n", "</pre>\n"], "text/plain": ["Collecting murmurhash<1.1.0,>=0.28.0 (from spacy)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">  Downloading murmurhash-1.0.13-cp313-cp313-win_amd64.whl.metadata (2.2 kB)\n", "</pre>\n"], "text/plain": ["  Downloading murmurhash-1.0.13-cp313-cp313-win_amd64.whl.metadata (2.2 kB)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Collecting cymem&lt;2.1.0,&gt;=2.0.2 (from spacy)\n", "</pre>\n"], "text/plain": ["Collecting cymem<2.1.0,>=2.0.2 (from spacy)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">  Downloading cymem-2.0.11-cp313-cp313-win_amd64.whl.metadata (8.8 kB)\n", "</pre>\n"], "text/plain": ["  Downloading cymem-2.0.11-cp313-cp313-win_amd64.whl.metadata (8.8 kB)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Collecting preshed&lt;3.1.0,&gt;=3.0.2 (from spacy)\n", "</pre>\n"], "text/plain": ["Collecting preshed<3.1.0,>=3.0.2 (from spacy)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">  Downloading preshed-3.0.10-cp313-cp313-win_amd64.whl.metadata (2.5 kB)\n", "</pre>\n"], "text/plain": ["  Downloading preshed-3.0.10-cp313-cp313-win_amd64.whl.metadata (2.5 kB)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Collecting thinc&lt;8.4.0,&gt;=8.3.4 (from spacy)\n", "</pre>\n"], "text/plain": ["Collecting thinc<8.4.0,>=8.3.4 (from spacy)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">  Downloading thinc-8.3.6-cp313-cp313-win_amd64.whl.metadata (15 kB)\n", "</pre>\n"], "text/plain": ["  Downloading thinc-8.3.6-cp313-cp313-win_amd64.whl.metadata (15 kB)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Collecting wasabi&lt;1.2.0,&gt;=0.9.1 (from spacy)\n", "</pre>\n"], "text/plain": ["Collecting wasabi<1.2.0,>=0.9.1 (from spacy)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">  Downloading wasabi-1.1.3-py3-none-any.whl.metadata (28 kB)\n", "</pre>\n"], "text/plain": ["  Downloading wasabi-1.1.3-py3-none-any.whl.metadata (28 kB)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Collecting srsly&lt;3.0.0,&gt;=2.4.3 (from spacy)\n", "</pre>\n"], "text/plain": ["Collecting srsly<3.0.0,>=2.4.3 (from spacy)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">  Downloading srsly-2.5.1-cp313-cp313-win_amd64.whl.metadata (20 kB)\n", "</pre>\n"], "text/plain": ["  Downloading srsly-2.5.1-cp313-cp313-win_amd64.whl.metadata (20 kB)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Collecting catalogue&lt;2.1.0,&gt;=2.0.6 (from spacy)\n", "</pre>\n"], "text/plain": ["Collecting catalogue<2.1.0,>=2.0.6 (from spacy)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">  Downloading catalogue-2.0.10-py3-none-any.whl.metadata (14 kB)\n", "</pre>\n"], "text/plain": ["  Downloading catalogue-2.0.10-py3-none-any.whl.metadata (14 kB)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Collecting weasel&lt;0.5.0,&gt;=0.1.0 (from spacy)\n", "</pre>\n"], "text/plain": ["Collecting weasel<0.5.0,>=0.1.0 (from spacy)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">  Downloading weasel-0.4.1-py3-none-any.whl.metadata (4.6 kB)\n", "</pre>\n"], "text/plain": ["  Downloading weasel-0.4.1-py3-none-any.whl.metadata (4.6 kB)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Collecting typer&lt;1.0.0,&gt;=0.3.0 (from spacy)\n", "</pre>\n"], "text/plain": ["Collecting typer<1.0.0,>=0.3.0 (from spacy)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">  Downloading typer-0.16.0-py3-none-any.whl.metadata (15 kB)\n", "</pre>\n"], "text/plain": ["  Downloading typer-0.16.0-py3-none-any.whl.metadata (15 kB)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Requirement already satisfied: tqdm&lt;5.0.0,&gt;=4.38.0 in c:\\users\\<USER>\\appdata\\roaming\\python\\python313\\site-packages (from spacy) (4.67.1)\n", "</pre>\n"], "text/plain": ["Requirement already satisfied: tqdm<5.0.0,>=4.38.0 in c:\\users\\<USER>\\appdata\\roaming\\python\\python313\\site-packages (from spacy) (4.67.1)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Requirement already satisfied: numpy&gt;=1.19.0 in c:\\users\\<USER>\\appdata\\roaming\\python\\python313\\site-packages (from spacy) (2.2.6)\n", "</pre>\n"], "text/plain": ["Requirement already satisfied: numpy>=1.19.0 in c:\\users\\<USER>\\appdata\\roaming\\python\\python313\\site-packages (from spacy) (2.2.6)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Requirement already satisfied: requests&lt;3.0.0,&gt;=2.13.0 in c:\\users\\<USER>\\appdata\\roaming\\python\\python313\\site-packages (from spacy) (2.31.0)\n", "</pre>\n"], "text/plain": ["Requirement already satisfied: requests<3.0.0,>=2.13.0 in c:\\users\\<USER>\\appdata\\roaming\\python\\python313\\site-packages (from spacy) (2.31.0)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Requirement already satisfied: pydantic!=1.8,!=1.8.1,&lt;3.0.0,&gt;=1.7.4 in c:\\users\\<USER>\\appdata\\roaming\\python\\python313\\site-packages (from spacy) (2.11.5)\n", "</pre>\n"], "text/plain": ["Requirement already satisfied: pydantic!=1.8,!=1.8.1,<3.0.0,>=1.7.4 in c:\\users\\<USER>\\appdata\\roaming\\python\\python313\\site-packages (from spacy) (2.11.5)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Requirement already satisfied: jinja2 in c:\\users\\<USER>\\appdata\\roaming\\python\\python313\\site-packages (from spacy) (3.1.6)\n", "</pre>\n"], "text/plain": ["Requirement already satisfied: jinja2 in c:\\users\\<USER>\\appdata\\roaming\\python\\python313\\site-packages (from spacy) (3.1.6)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Requirement already satisfied: setuptools in c:\\users\\<USER>\\appdata\\roaming\\python\\python313\\site-packages (from spacy) (80.9.0)\n", "</pre>\n"], "text/plain": ["Requirement already satisfied: setuptools in c:\\users\\<USER>\\appdata\\roaming\\python\\python313\\site-packages (from spacy) (80.9.0)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Requirement already satisfied: packaging&gt;=20.0 in c:\\users\\<USER>\\appdata\\roaming\\python\\python313\\site-packages (from spacy) (24.2)\n", "</pre>\n"], "text/plain": ["Requirement already satisfied: packaging>=20.0 in c:\\users\\<USER>\\appdata\\roaming\\python\\python313\\site-packages (from spacy) (24.2)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Collecting langcodes&lt;4.0.0,&gt;=3.2.0 (from spacy)\n", "</pre>\n"], "text/plain": ["Collecting langcodes<4.0.0,>=3.2.0 (from spacy)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">  Downloading langcodes-3.5.0-py3-none-any.whl.metadata (29 kB)\n", "</pre>\n"], "text/plain": ["  Downloading langcodes-3.5.0-py3-none-any.whl.metadata (29 kB)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Collecting language-data&gt;=1.2 (from langcodes&lt;4.0.0,&gt;=3.2.0-&gt;spacy)\n", "</pre>\n"], "text/plain": ["Collecting language-data>=1.2 (from langcodes<4.0.0,>=3.2.0->spacy)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">  Downloading language_data-1.3.0-py3-none-any.whl.metadata (4.3 kB)\n", "</pre>\n"], "text/plain": ["  Downloading language_data-1.3.0-py3-none-any.whl.metadata (4.3 kB)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Requirement already satisfied: annotated-types&gt;=0.6.0 in c:\\users\\<USER>\\appdata\\roaming\\python\\python313\\site-packages (from pydantic!=1.8,!=1.8.1,&lt;3.0.0,&gt;=1.7.4-&gt;spacy) (0.7.0)\n", "</pre>\n"], "text/plain": ["Requirement already satisfied: annotated-types>=0.6.0 in c:\\users\\<USER>\\appdata\\roaming\\python\\python313\\site-packages (from pydantic!=1.8,!=1.8.1,<3.0.0,>=1.7.4->spacy) (0.7.0)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Requirement already satisfied: pydantic-core==2.33.2 in c:\\users\\<USER>\\appdata\\roaming\\python\\python313\\site-packages (from pydantic!=1.8,!=1.8.1,&lt;3.0.0,&gt;=1.7.4-&gt;spacy) (2.33.2)\n", "</pre>\n"], "text/plain": ["Requirement already satisfied: pydantic-core==2.33.2 in c:\\users\\<USER>\\appdata\\roaming\\python\\python313\\site-packages (from pydantic!=1.8,!=1.8.1,<3.0.0,>=1.7.4->spacy) (2.33.2)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Requirement already satisfied: typing-extensions&gt;=4.12.2 in c:\\users\\<USER>\\appdata\\roaming\\python\\python313\\site-packages (from pydantic!=1.8,!=1.8.1,&lt;3.0.0,&gt;=1.7.4-&gt;spacy) (4.14.0)\n", "</pre>\n"], "text/plain": ["Requirement already satisfied: typing-extensions>=4.12.2 in c:\\users\\<USER>\\appdata\\roaming\\python\\python313\\site-packages (from pydantic!=1.8,!=1.8.1,<3.0.0,>=1.7.4->spacy) (4.14.0)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Requirement already satisfied: typing-inspection&gt;=0.4.0 in c:\\users\\<USER>\\appdata\\roaming\\python\\python313\\site-packages (from pydantic!=1.8,!=1.8.1,&lt;3.0.0,&gt;=1.7.4-&gt;spacy) (0.4.1)\n", "</pre>\n"], "text/plain": ["Requirement already satisfied: typing-inspection>=0.4.0 in c:\\users\\<USER>\\appdata\\roaming\\python\\python313\\site-packages (from pydantic!=1.8,!=1.8.1,<3.0.0,>=1.7.4->spacy) (0.4.1)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Requirement already satisfied: charset-normalizer&lt;4,&gt;=2 in c:\\users\\<USER>\\appdata\\roaming\\python\\python313\\site-packages (from requests&lt;3.0.0,&gt;=2.13.0-&gt;spacy) (3.4.2)\n", "</pre>\n"], "text/plain": ["Requirement already satisfied: charset-normalizer<4,>=2 in c:\\users\\<USER>\\appdata\\roaming\\python\\python313\\site-packages (from requests<3.0.0,>=2.13.0->spacy) (3.4.2)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Requirement already satisfied: idna&lt;4,&gt;=2.5 in c:\\users\\<USER>\\appdata\\roaming\\python\\python313\\site-packages (from requests&lt;3.0.0,&gt;=2.13.0-&gt;spacy) (3.10)\n", "</pre>\n"], "text/plain": ["Requirement already satisfied: idna<4,>=2.5 in c:\\users\\<USER>\\appdata\\roaming\\python\\python313\\site-packages (from requests<3.0.0,>=2.13.0->spacy) (3.10)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Requirement already satisfied: urllib3&lt;3,&gt;=1.21.1 in c:\\users\\<USER>\\appdata\\roaming\\python\\python313\\site-packages (from requests&lt;3.0.0,&gt;=2.13.0-&gt;spacy) (2.4.0)\n", "</pre>\n"], "text/plain": ["Requirement already satisfied: urllib3<3,>=1.21.1 in c:\\users\\<USER>\\appdata\\roaming\\python\\python313\\site-packages (from requests<3.0.0,>=2.13.0->spacy) (2.4.0)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Requirement already satisfied: certifi&gt;=2017.4.17 in c:\\users\\<USER>\\appdata\\roaming\\python\\python313\\site-packages (from requests&lt;3.0.0,&gt;=2.13.0-&gt;spacy) (2025.4.26)\n", "</pre>\n"], "text/plain": ["Requirement already satisfied: certifi>=2017.4.17 in c:\\users\\<USER>\\appdata\\roaming\\python\\python313\\site-packages (from requests<3.0.0,>=2.13.0->spacy) (2025.4.26)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Collecting blis&lt;1.4.0,&gt;=1.3.0 (from thinc&lt;8.4.0,&gt;=8.3.4-&gt;spacy)\n", "</pre>\n"], "text/plain": ["Collecting blis<1.4.0,>=1.3.0 (from thinc<8.4.0,>=8.3.4->spacy)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">  Downloading blis-1.3.0-cp313-cp313-win_amd64.whl.metadata (7.6 kB)\n", "</pre>\n"], "text/plain": ["  Downloading blis-1.3.0-cp313-cp313-win_amd64.whl.metadata (7.6 kB)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Collecting confection&lt;1.0.0,&gt;=0.0.1 (from thinc&lt;8.4.0,&gt;=8.3.4-&gt;spacy)\n", "</pre>\n"], "text/plain": ["Collecting confection<1.0.0,>=0.0.1 (from thinc<8.4.0,>=8.3.4->spacy)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">  Downloading confection-0.1.5-py3-none-any.whl.metadata (19 kB)\n", "</pre>\n"], "text/plain": ["  Downloading confection-0.1.5-py3-none-any.whl.metadata (19 kB)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Requirement already satisfied: colorama in c:\\users\\<USER>\\appdata\\roaming\\python\\python313\\site-packages (from tqdm&lt;5.0.0,&gt;=4.38.0-&gt;spacy) (0.4.6)\n", "</pre>\n"], "text/plain": ["Requirement already satisfied: colorama in c:\\users\\<USER>\\appdata\\roaming\\python\\python313\\site-packages (from tqdm<5.0.0,>=4.38.0->spacy) (0.4.6)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Requirement already satisfied: click&gt;=8.0.0 in c:\\users\\<USER>\\appdata\\roaming\\python\\python313\\site-packages (from typer&lt;1.0.0,&gt;=0.3.0-&gt;spacy) (8.2.2)\n", "</pre>\n"], "text/plain": ["Requirement already satisfied: click>=8.0.0 in c:\\users\\<USER>\\appdata\\roaming\\python\\python313\\site-packages (from typer<1.0.0,>=0.3.0->spacy) (8.2.2)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Collecting shellingham&gt;=1.3.0 (from typer&lt;1.0.0,&gt;=0.3.0-&gt;spacy)\n", "</pre>\n"], "text/plain": ["Collecting shellingham>=1.3.0 (from typer<1.0.0,>=0.3.0->spacy)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">  Downloading shellingham-1.5.4-py2.py3-none-any.whl.metadata (3.5 kB)\n", "</pre>\n"], "text/plain": ["  Downloading shellingham-1.5.4-py2.py3-none-any.whl.metadata (3.5 kB)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Collecting rich&gt;=10.11.0 (from typer&lt;1.0.0,&gt;=0.3.0-&gt;spacy)\n", "</pre>\n"], "text/plain": ["Collecting rich>=10.11.0 (from typer<1.0.0,>=0.3.0->spacy)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">  Downloading rich-14.1.0-py3-none-any.whl.metadata (18 kB)\n", "</pre>\n"], "text/plain": ["  Downloading rich-14.1.0-py3-none-any.whl.metadata (18 kB)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Collecting cloudpathlib&lt;1.0.0,&gt;=0.7.0 (from weasel&lt;0.5.0,&gt;=0.1.0-&gt;spacy)\n", "</pre>\n"], "text/plain": ["Collecting cloudpathlib<1.0.0,>=0.7.0 (from weasel<0.5.0,>=0.1.0->spacy)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">  Downloading cloudpathlib-0.21.1-py3-none-any.whl.metadata (14 kB)\n", "</pre>\n"], "text/plain": ["  Downloading cloudpathlib-0.21.1-py3-none-any.whl.metadata (14 kB)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Collecting smart-open&lt;8.0.0,&gt;=5.2.1 (from weasel&lt;0.5.0,&gt;=0.1.0-&gt;spacy)\n", "</pre>\n"], "text/plain": ["Collecting smart-open<8.0.0,>=5.2.1 (from weasel<0.5.0,>=0.1.0->spacy)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">  Downloading smart_open-7.3.0.post1-py3-none-any.whl.metadata (24 kB)\n", "</pre>\n"], "text/plain": ["  Downloading smart_open-7.3.0.post1-py3-none-any.whl.metadata (24 kB)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Collecting wrapt (from smart-open&lt;8.0.0,&gt;=5.2.1-&gt;weasel&lt;0.5.0,&gt;=0.1.0-&gt;spacy)\n", "</pre>\n"], "text/plain": ["Collecting wrapt (from smart-open<8.0.0,>=5.2.1->weasel<0.5.0,>=0.1.0->spacy)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">  Downloading wrapt-1.17.2-cp313-cp313-win_amd64.whl.metadata (6.5 kB)\n", "</pre>\n"], "text/plain": ["  Downloading wrapt-1.17.2-cp313-cp313-win_amd64.whl.metadata (6.5 kB)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Collecting marisa-trie&gt;=1.1.0 (from language-data&gt;=1.2-&gt;langcodes&lt;4.0.0,&gt;=3.2.0-&gt;spacy)\n", "</pre>\n"], "text/plain": ["Collecting marisa-trie>=1.1.0 (from language-data>=1.2->langcodes<4.0.0,>=3.2.0->spacy)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">  Downloading marisa_trie-1.2.1-cp313-cp313-win_amd64.whl.metadata (9.3 kB)\n", "</pre>\n"], "text/plain": ["  Downloading marisa_trie-1.2.1-cp313-cp313-win_amd64.whl.metadata (9.3 kB)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Collecting markdown-it-py&gt;=2.2.0 (from rich&gt;=10.11.0-&gt;typer&lt;1.0.0,&gt;=0.3.0-&gt;spacy)\n", "</pre>\n"], "text/plain": ["Collecting markdown-it-py>=2.2.0 (from rich>=10.11.0->typer<1.0.0,>=0.3.0->spacy)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">  Downloading markdown_it_py-3.0.0-py3-none-any.whl.metadata (6.9 kB)\n", "</pre>\n"], "text/plain": ["  Downloading markdown_it_py-3.0.0-py3-none-any.whl.metadata (6.9 kB)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Requirement already satisfied: pygments&lt;3.0.0,&gt;=2.13.0 in c:\\users\\<USER>\\appdata\\roaming\\python\\python313\\site-packages (from rich&gt;=10.11.0-&gt;typer&lt;1.0.0,&gt;=0.3.0-&gt;spacy) (2.19.2)\n", "</pre>\n"], "text/plain": ["Requirement already satisfied: pygments<3.0.0,>=2.13.0 in c:\\users\\<USER>\\appdata\\roaming\\python\\python313\\site-packages (from rich>=10.11.0->typer<1.0.0,>=0.3.0->spacy) (2.19.2)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Collecting mdurl~=0.1 (from markdown-it-py&gt;=2.2.0-&gt;rich&gt;=10.11.0-&gt;typer&lt;1.0.0,&gt;=0.3.0-&gt;spacy)\n", "</pre>\n"], "text/plain": ["Collecting mdurl~=0.1 (from markdown-it-py>=2.2.0->rich>=10.11.0->typer<1.0.0,>=0.3.0->spacy)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">  Downloading mdurl-0.1.2-py3-none-any.whl.metadata (1.6 kB)\n", "</pre>\n"], "text/plain": ["  Downloading mdurl-0.1.2-py3-none-any.whl.metadata (1.6 kB)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Requirement already satisfied: MarkupSafe&gt;=2.0 in c:\\users\\<USER>\\appdata\\roaming\\python\\python313\\site-packages (from jinja2-&gt;spacy) (3.0.2)\n", "</pre>\n"], "text/plain": ["Requirement already satisfied: MarkupSafe>=2.0 in c:\\users\\<USER>\\appdata\\roaming\\python\\python313\\site-packages (from jinja2->spacy) (3.0.2)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Downloading spacy-3.8.7-cp313-cp313-win_amd64.whl (13.9 MB)\n", "</pre>\n"], "text/plain": ["Downloading spacy-3.8.7-cp313-cp313-win_amd64.whl (13.9 MB)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pip\\_vendor\\rich\\live.py:256: UserWarning: install \n", "\"ipywidgets\" for <PERSON><PERSON><PERSON> support\n", "  warnings.warn('install \"ipywidgets\" for Jupyter support')\n", "</pre>\n"], "text/plain": ["C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pip\\_vendor\\rich\\live.py:256: UserWarning: install \n", "\"ipywidgets\" for <PERSON><PERSON><PERSON> support\n", "  warnings.warn('install \"ipywidgets\" for Jupyter support')\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"], "text/plain": []}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Downloading catalogue-2.0.10-py3-none-any.whl (17 kB)\n", "</pre>\n"], "text/plain": ["Downloading catalogue-2.0.10-py3-none-any.whl (17 kB)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Downloading cymem-2.0.11-cp313-cp313-win_amd64.whl (39 kB)\n", "</pre>\n"], "text/plain": ["Downloading cymem-2.0.11-cp313-cp313-win_amd64.whl (39 kB)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Downloading langcodes-3.5.0-py3-none-any.whl (182 kB)\n", "</pre>\n"], "text/plain": ["Downloading langcodes-3.5.0-py3-none-any.whl (182 kB)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Downloading murmurhash-1.0.13-cp313-cp313-win_amd64.whl (24 kB)\n", "</pre>\n"], "text/plain": ["Downloading murmurhash-1.0.13-cp313-cp313-win_amd64.whl (24 kB)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Downloading preshed-3.0.10-cp313-cp313-win_amd64.whl (115 kB)\n", "</pre>\n"], "text/plain": ["Downloading preshed-3.0.10-cp313-cp313-win_amd64.whl (115 kB)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Downloading spacy_legacy-3.0.12-py2.py3-none-any.whl (29 kB)\n", "</pre>\n"], "text/plain": ["Downloading spacy_legacy-3.0.12-py2.py3-none-any.whl (29 kB)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Downloading spacy_loggers-1.0.5-py3-none-any.whl (22 kB)\n", "</pre>\n"], "text/plain": ["Downloading spacy_loggers-1.0.5-py3-none-any.whl (22 kB)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Downloading srsly-2.5.1-cp313-cp313-win_amd64.whl (630 kB)\n", "</pre>\n"], "text/plain": ["Downloading srsly-2.5.1-cp313-cp313-win_amd64.whl (630 kB)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"], "text/plain": []}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Downloading thinc-8.3.6-cp313-cp313-win_amd64.whl (1.7 MB)\n", "</pre>\n"], "text/plain": ["Downloading thinc-8.3.6-cp313-cp313-win_amd64.whl (1.7 MB)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"], "text/plain": []}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Downloading blis-1.3.0-cp313-cp313-win_amd64.whl (6.3 MB)\n", "</pre>\n"], "text/plain": ["Downloading blis-1.3.0-cp313-cp313-win_amd64.whl (6.3 MB)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"], "text/plain": []}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Downloading confection-0.1.5-py3-none-any.whl (35 kB)\n", "</pre>\n"], "text/plain": ["Downloading confection-0.1.5-py3-none-any.whl (35 kB)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Downloading typer-0.16.0-py3-none-any.whl (46 kB)\n", "</pre>\n"], "text/plain": ["Downloading typer-0.16.0-py3-none-any.whl (46 kB)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Downloading wasabi-1.1.3-py3-none-any.whl (27 kB)\n", "</pre>\n"], "text/plain": ["Downloading wasabi-1.1.3-py3-none-any.whl (27 kB)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Downloading weasel-0.4.1-py3-none-any.whl (50 kB)\n", "</pre>\n"], "text/plain": ["Downloading weasel-0.4.1-py3-none-any.whl (50 kB)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Downloading cloudpathlib-0.21.1-py3-none-any.whl (52 kB)\n", "</pre>\n"], "text/plain": ["Downloading cloudpathlib-0.21.1-py3-none-any.whl (52 kB)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Downloading smart_open-7.3.0.post1-py3-none-any.whl (61 kB)\n", "</pre>\n"], "text/plain": ["Downloading smart_open-7.3.0.post1-py3-none-any.whl (61 kB)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Downloading language_data-1.3.0-py3-none-any.whl (5.4 MB)\n", "</pre>\n"], "text/plain": ["Downloading language_data-1.3.0-py3-none-any.whl (5.4 MB)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"], "text/plain": []}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Downloading marisa_trie-1.2.1-cp313-cp313-win_amd64.whl (149 kB)\n", "</pre>\n"], "text/plain": ["Downloading marisa_trie-1.2.1-cp313-cp313-win_amd64.whl (149 kB)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Downloading rich-14.1.0-py3-none-any.whl (243 kB)\n", "</pre>\n"], "text/plain": ["Downloading rich-14.1.0-py3-none-any.whl (243 kB)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Downloading markdown_it_py-3.0.0-py3-none-any.whl (87 kB)\n", "</pre>\n"], "text/plain": ["Downloading markdown_it_py-3.0.0-py3-none-any.whl (87 kB)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Downloading mdurl-0.1.2-py3-none-any.whl (10.0 kB)\n", "</pre>\n"], "text/plain": ["Downloading mdurl-0.1.2-py3-none-any.whl (10.0 kB)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Downloading shellingham-1.5.4-py2.py3-none-any.whl (9.8 kB)\n", "</pre>\n"], "text/plain": ["Downloading shellingham-1.5.4-py2.py3-none-any.whl (9.8 kB)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Downloading wrapt-1.17.2-cp313-cp313-win_amd64.whl (38 kB)\n", "</pre>\n"], "text/plain": ["Downloading wrapt-1.17.2-cp313-cp313-win_amd64.whl (38 kB)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Installing collected packages: cymem, wrapt, wasabi, spacy-loggers, spacy-legacy, shellingham, murmurhash, mdurl, marisa-trie, cloudpathlib, catalogue, blis, srsly, smart-open, preshed, markdown-it-py, language-data, rich, langcodes, confection, typer, thinc, weasel, spacy\n", "</pre>\n"], "text/plain": ["Installing collected packages: cymem, wrapt, wasabi, spacy-loggers, spacy-legacy, shellingham, murmurhash, mdurl, marisa-trie, cloudpathlib, catalogue, blis, srsly, smart-open, preshed, markdown-it-py, language-data, rich, langcodes, confection, typer, thinc, weasel, spacy\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pip\\_vendor\\rich\\live.py:256: UserWarning: install \"ipywidgets\" for Jupyter support\n", "  warnings.warn('install \"ipywidgets\" for Jupyter support')\n", "</pre>\n"], "text/plain": ["C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pip\\_vendor\\rich\\live.py:256: UserWarning: install \"ipywidgets\" for Jupyter support\n", "  warnings.warn('install \"ipywidgets\" for Jupyter support')\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pip\\_vendor\\rich\\live.py:256: UserWarning: install \"ipywidgets\" for Jupyter support\n", "  warnings.warn('install \"ipywidgets\" for Jupyter support')\n", "</pre>\n"], "text/plain": ["C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pip\\_vendor\\rich\\live.py:256: UserWarning: install \"ipywidgets\" for Jupyter support\n", "  warnings.warn('install \"ipywidgets\" for Jupyter support')\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pip\\_vendor\\rich\\live.py:256: UserWarning: install \"ipywidgets\" for Jupyter support\n", "  warnings.warn('install \"ipywidgets\" for Jupyter support')\n", "</pre>\n"], "text/plain": ["C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pip\\_vendor\\rich\\live.py:256: UserWarning: install \"ipywidgets\" for Jupyter support\n", "  warnings.warn('install \"ipywidgets\" for Jupyter support')\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pip\\_vendor\\rich\\live.py:256: UserWarning: install \"ipywidgets\" for Jupyter support\n", "  warnings.warn('install \"ipywidgets\" for Jupyter support')\n", "</pre>\n"], "text/plain": ["C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pip\\_vendor\\rich\\live.py:256: UserWarning: install \"ipywidgets\" for Jupyter support\n", "  warnings.warn('install \"ipywidgets\" for Jupyter support')\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pip\\_vendor\\rich\\live.py:256: UserWarning: install \"ipywidgets\" for Jupyter support\n", "  warnings.warn('install \"ipywidgets\" for Jupyter support')\n", "</pre>\n"], "text/plain": ["C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pip\\_vendor\\rich\\live.py:256: UserWarning: install \"ipywidgets\" for Jupyter support\n", "  warnings.warn('install \"ipywidgets\" for Jupyter support')\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pip\\_vendor\\rich\\live.py:256: UserWarning: install \"ipywidgets\" for Jupyter support\n", "  warnings.warn('install \"ipywidgets\" for Jupyter support')\n", "</pre>\n"], "text/plain": ["C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pip\\_vendor\\rich\\live.py:256: UserWarning: install \"ipywidgets\" for Jupyter support\n", "  warnings.warn('install \"ipywidgets\" for Jupyter support')\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pip\\_vendor\\rich\\live.py:256: UserWarning: install \"ipywidgets\" for Jupyter support\n", "  warnings.warn('install \"ipywidgets\" for Jupyter support')\n", "</pre>\n"], "text/plain": ["C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pip\\_vendor\\rich\\live.py:256: UserWarning: install \"ipywidgets\" for Jupyter support\n", "  warnings.warn('install \"ipywidgets\" for Jupyter support')\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pip\\_vendor\\rich\\live.py:256: UserWarning: install \"ipywidgets\" for Jupyter support\n", "  warnings.warn('install \"ipywidgets\" for Jupyter support')\n", "</pre>\n"], "text/plain": ["C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pip\\_vendor\\rich\\live.py:256: UserWarning: install \"ipywidgets\" for Jupyter support\n", "  warnings.warn('install \"ipywidgets\" for Jupyter support')\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pip\\_vendor\\rich\\live.py:256: UserWarning: install \"ipywidgets\" for Jupyter support\n", "  warnings.warn('install \"ipywidgets\" for Jupyter support')\n", "</pre>\n"], "text/plain": ["C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pip\\_vendor\\rich\\live.py:256: UserWarning: install \"ipywidgets\" for Jupyter support\n", "  warnings.warn('install \"ipywidgets\" for Jupyter support')\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pip\\_vendor\\rich\\live.py:256: UserWarning: install \"ipywidgets\" for Jupyter support\n", "  warnings.warn('install \"ipywidgets\" for Jupyter support')\n", "</pre>\n"], "text/plain": ["C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pip\\_vendor\\rich\\live.py:256: UserWarning: install \"ipywidgets\" for Jupyter support\n", "  warnings.warn('install \"ipywidgets\" for Jupyter support')\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"], "text/plain": []}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'<PERSON>jaVu Sans Mono',consol<PERSON>,'Courier New',monospace\">Successfully installed blis-1.3.0 catalogue-2.0.10 cloudpathlib-0.21.1 confection-0.1.5 cymem-2.0.11 langcodes-3.5.0 language-data-1.3.0 marisa-trie-1.2.1 markdown-it-py-3.0.0 mdurl-0.1.2 murmurhash-1.0.13 preshed-3.0.10 rich-14.1.0 shellingham-1.5.4 smart-open-7.3.0.post1 spacy-3.8.7 spacy-legacy-3.0.12 spacy-loggers-1.0.5 srsly-2.5.1 thinc-8.3.6 typer-0.16.0 wasabi-1.1.3 weasel-0.4.1 wrapt-1.17.2\n", "</pre>\n"], "text/plain": ["Successfully installed blis-1.3.0 catalogue-2.0.10 cloudpathlib-0.21.1 confection-0.1.5 cymem-2.0.11 langcodes-3.5.0 language-data-1.3.0 marisa-trie-1.2.1 markdown-it-py-3.0.0 mdurl-0.1.2 murmurhash-1.0.13 preshed-3.0.10 rich-14.1.0 shellingham-1.5.4 smart-open-7.3.0.post1 spacy-3.8.7 spacy-legacy-3.0.12 spacy-loggers-1.0.5 srsly-2.5.1 thinc-8.3.6 typer-0.16.0 wasabi-1.1.3 weasel-0.4.1 wrapt-1.17.2\n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["spacy installed successfully.\n", "Installing nltk...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING: pip is being invoked by an old script wrapper. This will fail in a future version of pip.\n", "Please see https://github.com/pypa/pip/issues/5599 for advice on fixing the underlying issue.\n", "To avoid this problem you can invoke Python with '-m pip' instead of running pip directly.\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Defaulting to user installation because normal site-packages is not writeable\n", "</pre>\n"], "text/plain": ["Defaulting to user installation because normal site-packages is not writeable\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Collecting nltk\n", "</pre>\n"], "text/plain": ["Collecting nltk\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">  Downloading nltk-3.9.1-py3-none-any.whl.metadata (2.9 kB)\n", "</pre>\n"], "text/plain": ["  Downloading nltk-3.9.1-py3-none-any.whl.metadata (2.9 kB)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Requirement already satisfied: click in c:\\users\\<USER>\\appdata\\roaming\\python\\python313\\site-packages (from nltk) (8.2.2)\n", "</pre>\n"], "text/plain": ["Requirement already satisfied: click in c:\\users\\<USER>\\appdata\\roaming\\python\\python313\\site-packages (from nltk) (8.2.2)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Collecting joblib (from nltk)\n", "</pre>\n"], "text/plain": ["Collecting joblib (from nltk)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">  Downloading joblib-1.5.1-py3-none-any.whl.metadata (5.6 kB)\n", "</pre>\n"], "text/plain": ["  Downloading joblib-1.5.1-py3-none-any.whl.metadata (5.6 kB)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Requirement already satisfied: regex&gt;=2021.8.3 in c:\\users\\<USER>\\appdata\\roaming\\python\\python313\\site-packages (from nltk) (2024.11.6)\n", "</pre>\n"], "text/plain": ["Requirement already satisfied: regex>=2021.8.3 in c:\\users\\<USER>\\appdata\\roaming\\python\\python313\\site-packages (from nltk) (2024.11.6)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Requirement already satisfied: tqdm in c:\\users\\<USER>\\appdata\\roaming\\python\\python313\\site-packages (from nltk) (4.67.1)\n", "</pre>\n"], "text/plain": ["Requirement already satisfied: tqdm in c:\\users\\<USER>\\appdata\\roaming\\python\\python313\\site-packages (from nltk) (4.67.1)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Requirement already satisfied: colorama in c:\\users\\<USER>\\appdata\\roaming\\python\\python313\\site-packages (from click-&gt;nltk) (0.4.6)\n", "</pre>\n"], "text/plain": ["Requirement already satisfied: colorama in c:\\users\\<USER>\\appdata\\roaming\\python\\python313\\site-packages (from click->nltk) (0.4.6)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Downloading nltk-3.9.1-py3-none-any.whl (1.5 MB)\n", "</pre>\n"], "text/plain": ["Downloading nltk-3.9.1-py3-none-any.whl (1.5 MB)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pip\\_vendor\\rich\\live.py:256: UserWarning: install \n", "\"ipywidgets\" for <PERSON><PERSON><PERSON> support\n", "  warnings.warn('install \"ipywidgets\" for Jupyter support')\n", "</pre>\n"], "text/plain": ["C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pip\\_vendor\\rich\\live.py:256: UserWarning: install \n", "\"ipywidgets\" for <PERSON><PERSON><PERSON> support\n", "  warnings.warn('install \"ipywidgets\" for Jupyter support')\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"], "text/plain": []}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Downloading joblib-1.5.1-py3-none-any.whl (307 kB)\n", "</pre>\n"], "text/plain": ["Downloading joblib-1.5.1-py3-none-any.whl (307 kB)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Installing collected packages: joblib, nltk\n", "</pre>\n"], "text/plain": ["Installing collected packages: joblib, nltk\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pip\\_vendor\\rich\\live.py:256: UserWarning: install \"ipywidgets\" for Jupyter support\n", "  warnings.warn('install \"ipywidgets\" for Jupyter support')\n", "</pre>\n"], "text/plain": ["C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pip\\_vendor\\rich\\live.py:256: UserWarning: install \"ipywidgets\" for Jupyter support\n", "  warnings.warn('install \"ipywidgets\" for Jupyter support')\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pip\\_vendor\\rich\\live.py:256: UserWarning: install \"ipywidgets\" for Jupyter support\n", "  warnings.warn('install \"ipywidgets\" for Jupyter support')\n", "</pre>\n"], "text/plain": ["C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pip\\_vendor\\rich\\live.py:256: UserWarning: install \"ipywidgets\" for Jupyter support\n", "  warnings.warn('install \"ipywidgets\" for Jupyter support')\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"], "text/plain": []}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Successfully installed joblib-1.5.1 nltk-3.9.1\n", "</pre>\n"], "text/plain": ["Successfully installed joblib-1.5.1 nltk-3.9.1\n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["nltk installed successfully.\n", "Installing pandas...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING: pip is being invoked by an old script wrapper. This will fail in a future version of pip.\n", "Please see https://github.com/pypa/pip/issues/5599 for advice on fixing the underlying issue.\n", "To avoid this problem you can invoke Python with '-m pip' instead of running pip directly.\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Defaulting to user installation because normal site-packages is not writeable\n", "</pre>\n"], "text/plain": ["Defaulting to user installation because normal site-packages is not writeable\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Collecting pandas\n", "</pre>\n"], "text/plain": ["Collecting pandas\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">  Downloading pandas-2.3.1-cp313-cp313-win_amd64.whl.metadata (19 kB)\n", "</pre>\n"], "text/plain": ["  Downloading pandas-2.3.1-cp313-cp313-win_amd64.whl.metadata (19 kB)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Requirement already satisfied: numpy&gt;=1.26.0 in c:\\users\\<USER>\\appdata\\roaming\\python\\python313\\site-packages (from pandas) (2.2.6)\n", "</pre>\n"], "text/plain": ["Requirement already satisfied: numpy>=1.26.0 in c:\\users\\<USER>\\appdata\\roaming\\python\\python313\\site-packages (from pandas) (2.2.6)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Requirement already satisfied: python-dateutil&gt;=2.8.2 in c:\\users\\<USER>\\appdata\\roaming\\python\\python313\\site-packages (from pandas) (2.9.0.post0)\n", "</pre>\n"], "text/plain": ["Requirement already satisfied: python-dateutil>=2.8.2 in c:\\users\\<USER>\\appdata\\roaming\\python\\python313\\site-packages (from pandas) (2.9.0.post0)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Collecting pytz&gt;=2020.1 (from pandas)\n", "</pre>\n"], "text/plain": ["Collecting pytz>=2020.1 (from pandas)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">  Downloading pytz-2025.2-py2.py3-none-any.whl.metadata (22 kB)\n", "</pre>\n"], "text/plain": ["  Downloading pytz-2025.2-py2.py3-none-any.whl.metadata (22 kB)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Requirement already satisfied: tzdata&gt;=2022.7 in c:\\users\\<USER>\\appdata\\roaming\\python\\python313\\site-packages (from pandas) (2025.2)\n", "</pre>\n"], "text/plain": ["Requirement already satisfied: tzdata>=2022.7 in c:\\users\\<USER>\\appdata\\roaming\\python\\python313\\site-packages (from pandas) (2025.2)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Requirement already satisfied: six&gt;=1.5 in c:\\users\\<USER>\\appdata\\roaming\\python\\python313\\site-packages (from python-dateutil&gt;=2.8.2-&gt;pandas) (1.17.0)\n", "</pre>\n"], "text/plain": ["Requirement already satisfied: six>=1.5 in c:\\users\\<USER>\\appdata\\roaming\\python\\python313\\site-packages (from python-dateutil>=2.8.2->pandas) (1.17.0)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Downloading pandas-2.3.1-cp313-cp313-win_amd64.whl (11.0 MB)\n", "</pre>\n"], "text/plain": ["Downloading pandas-2.3.1-cp313-cp313-win_amd64.whl (11.0 MB)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pip\\_vendor\\rich\\live.py:256: UserWarning: install \n", "\"ipywidgets\" for <PERSON><PERSON><PERSON> support\n", "  warnings.warn('install \"ipywidgets\" for Jupyter support')\n", "</pre>\n"], "text/plain": ["C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pip\\_vendor\\rich\\live.py:256: UserWarning: install \n", "\"ipywidgets\" for <PERSON><PERSON><PERSON> support\n", "  warnings.warn('install \"ipywidgets\" for Jupyter support')\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"], "text/plain": []}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Downloading pytz-2025.2-py2.py3-none-any.whl (509 kB)\n", "</pre>\n"], "text/plain": ["Downloading pytz-2025.2-py2.py3-none-any.whl (509 kB)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Installing collected packages: pytz, pandas\n", "</pre>\n"], "text/plain": ["Installing collected packages: pytz, pandas\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pip\\_vendor\\rich\\live.py:256: UserWarning: install \"ipywidgets\" for Jupyter support\n", "  warnings.warn('install \"ipywidgets\" for Jupyter support')\n", "</pre>\n"], "text/plain": ["C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pip\\_vendor\\rich\\live.py:256: UserWarning: install \"ipywidgets\" for Jupyter support\n", "  warnings.warn('install \"ipywidgets\" for Jupyter support')\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"], "text/plain": []}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Successfully installed pandas-2.3.1 pytz-2025.2\n", "</pre>\n"], "text/plain": ["Successfully installed pandas-2.3.1 pytz-2025.2\n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["pandas installed successfully.\n", "plotly is already installed.\n", "networkx is already installed.\n", "Installing scikit-learn...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING: pip is being invoked by an old script wrapper. This will fail in a future version of pip.\n", "Please see https://github.com/pypa/pip/issues/5599 for advice on fixing the underlying issue.\n", "To avoid this problem you can invoke Python with '-m pip' instead of running pip directly.\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Defaulting to user installation because normal site-packages is not writeable\n", "</pre>\n"], "text/plain": ["Defaulting to user installation because normal site-packages is not writeable\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Collecting scikit-learn\n", "</pre>\n"], "text/plain": ["Collecting scikit-learn\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">  Downloading scikit_learn-1.7.1-cp313-cp313-win_amd64.whl.metadata (11 kB)\n", "</pre>\n"], "text/plain": ["  Downloading scikit_learn-1.7.1-cp313-cp313-win_amd64.whl.metadata (11 kB)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Requirement already satisfied: numpy&gt;=1.22.0 in c:\\users\\<USER>\\appdata\\roaming\\python\\python313\\site-packages (from scikit-learn) (2.2.6)\n", "</pre>\n"], "text/plain": ["Requirement already satisfied: numpy>=1.22.0 in c:\\users\\<USER>\\appdata\\roaming\\python\\python313\\site-packages (from scikit-learn) (2.2.6)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Collecting scipy&gt;=1.8.0 (from scikit-learn)\n", "</pre>\n"], "text/plain": ["Collecting scipy>=1.8.0 (from scikit-learn)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">  Downloading scipy-1.16.1-cp313-cp313-win_amd64.whl.metadata (60 kB)\n", "</pre>\n"], "text/plain": ["  Downloading scipy-1.16.1-cp313-cp313-win_amd64.whl.metadata (60 kB)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Requirement already satisfied: joblib&gt;=1.2.0 in c:\\users\\<USER>\\appdata\\roaming\\python\\python313\\site-packages (from scikit-learn) (1.5.1)\n", "</pre>\n"], "text/plain": ["Requirement already satisfied: joblib>=1.2.0 in c:\\users\\<USER>\\appdata\\roaming\\python\\python313\\site-packages (from scikit-learn) (1.5.1)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Collecting threadpoolctl&gt;=3.1.0 (from scikit-learn)\n", "</pre>\n"], "text/plain": ["Collecting threadpoolctl>=3.1.0 (from scikit-learn)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">  Downloading threadpoolctl-3.6.0-py3-none-any.whl.metadata (13 kB)\n", "</pre>\n"], "text/plain": ["  Downloading threadpoolctl-3.6.0-py3-none-any.whl.metadata (13 kB)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Downloading scikit_learn-1.7.1-cp313-cp313-win_amd64.whl (8.7 MB)\n", "</pre>\n"], "text/plain": ["Downloading scikit_learn-1.7.1-cp313-cp313-win_amd64.whl (8.7 MB)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pip\\_vendor\\rich\\live.py:256: UserWarning: install \n", "\"ipywidgets\" for <PERSON><PERSON><PERSON> support\n", "  warnings.warn('install \"ipywidgets\" for Jupyter support')\n", "</pre>\n"], "text/plain": ["C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pip\\_vendor\\rich\\live.py:256: UserWarning: install \n", "\"ipywidgets\" for <PERSON><PERSON><PERSON> support\n", "  warnings.warn('install \"ipywidgets\" for Jupyter support')\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"], "text/plain": []}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Downloading scipy-1.16.1-cp313-cp313-win_amd64.whl (38.5 MB)\n", "</pre>\n"], "text/plain": ["Downloading scipy-1.16.1-cp313-cp313-win_amd64.whl (38.5 MB)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"], "text/plain": []}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Downloading threadpoolctl-3.6.0-py3-none-any.whl (18 kB)\n", "</pre>\n"], "text/plain": ["Downloading threadpoolctl-3.6.0-py3-none-any.whl (18 kB)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Installing collected packages: threadpoolctl, scipy, scikit-learn\n", "</pre>\n"], "text/plain": ["Installing collected packages: threadpoolctl, scipy, scikit-learn\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pip\\_vendor\\rich\\live.py:256: UserWarning: install \"ipywidgets\" for Jupyter support\n", "  warnings.warn('install \"ipywidgets\" for Jupyter support')\n", "</pre>\n"], "text/plain": ["C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pip\\_vendor\\rich\\live.py:256: UserWarning: install \"ipywidgets\" for Jupyter support\n", "  warnings.warn('install \"ipywidgets\" for Jupyter support')\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pip\\_vendor\\rich\\live.py:256: UserWarning: install \"ipywidgets\" for Jupyter support\n", "  warnings.warn('install \"ipywidgets\" for Jupyter support')\n", "</pre>\n"], "text/plain": ["C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pip\\_vendor\\rich\\live.py:256: UserWarning: install \"ipywidgets\" for Jupyter support\n", "  warnings.warn('install \"ipywidgets\" for Jupyter support')\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"], "text/plain": []}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Successfully installed scikit-learn-1.7.1 scipy-1.16.1 threadpoolctl-3.6.0\n", "</pre>\n"], "text/plain": ["Successfully installed scikit-learn-1.7.1 scipy-1.16.1 threadpoolctl-3.6.0\n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["scikit-learn installed successfully.\n", "Installing textblob...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING: pip is being invoked by an old script wrapper. This will fail in a future version of pip.\n", "Please see https://github.com/pypa/pip/issues/5599 for advice on fixing the underlying issue.\n", "To avoid this problem you can invoke Python with '-m pip' instead of running pip directly.\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Defaulting to user installation because normal site-packages is not writeable\n", "</pre>\n"], "text/plain": ["Defaulting to user installation because normal site-packages is not writeable\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Collecting textblob\n", "</pre>\n"], "text/plain": ["Collecting textblob\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">  Downloading textblob-0.19.0-py3-none-any.whl.metadata (4.4 kB)\n", "</pre>\n"], "text/plain": ["  Downloading textblob-0.19.0-py3-none-any.whl.metadata (4.4 kB)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Requirement already satisfied: nltk&gt;=3.9 in c:\\users\\<USER>\\appdata\\roaming\\python\\python313\\site-packages (from textblob) (3.9.1)\n", "</pre>\n"], "text/plain": ["Requirement already satisfied: nltk>=3.9 in c:\\users\\<USER>\\appdata\\roaming\\python\\python313\\site-packages (from textblob) (3.9.1)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Requirement already satisfied: click in c:\\users\\<USER>\\appdata\\roaming\\python\\python313\\site-packages (from nltk&gt;=3.9-&gt;textblob) (8.2.2)\n", "</pre>\n"], "text/plain": ["Requirement already satisfied: click in c:\\users\\<USER>\\appdata\\roaming\\python\\python313\\site-packages (from nltk>=3.9->textblob) (8.2.2)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Requirement already satisfied: joblib in c:\\users\\<USER>\\appdata\\roaming\\python\\python313\\site-packages (from nltk&gt;=3.9-&gt;textblob) (1.5.1)\n", "</pre>\n"], "text/plain": ["Requirement already satisfied: joblib in c:\\users\\<USER>\\appdata\\roaming\\python\\python313\\site-packages (from nltk>=3.9->textblob) (1.5.1)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Requirement already satisfied: regex&gt;=2021.8.3 in c:\\users\\<USER>\\appdata\\roaming\\python\\python313\\site-packages (from nltk&gt;=3.9-&gt;textblob) (2024.11.6)\n", "</pre>\n"], "text/plain": ["Requirement already satisfied: regex>=2021.8.3 in c:\\users\\<USER>\\appdata\\roaming\\python\\python313\\site-packages (from nltk>=3.9->textblob) (2024.11.6)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Requirement already satisfied: tqdm in c:\\users\\<USER>\\appdata\\roaming\\python\\python313\\site-packages (from nltk&gt;=3.9-&gt;textblob) (4.67.1)\n", "</pre>\n"], "text/plain": ["Requirement already satisfied: tqdm in c:\\users\\<USER>\\appdata\\roaming\\python\\python313\\site-packages (from nltk>=3.9->textblob) (4.67.1)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Requirement already satisfied: colorama in c:\\users\\<USER>\\appdata\\roaming\\python\\python313\\site-packages (from click-&gt;nltk&gt;=3.9-&gt;textblob) (0.4.6)\n", "</pre>\n"], "text/plain": ["Requirement already satisfied: colorama in c:\\users\\<USER>\\appdata\\roaming\\python\\python313\\site-packages (from click->nltk>=3.9->textblob) (0.4.6)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Downloading textblob-0.19.0-py3-none-any.whl (624 kB)\n", "</pre>\n"], "text/plain": ["Downloading textblob-0.19.0-py3-none-any.whl (624 kB)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pip\\_vendor\\rich\\live.py:256: UserWarning: install \n", "\"ipywidgets\" for <PERSON><PERSON><PERSON> support\n", "  warnings.warn('install \"ipywidgets\" for Jupyter support')\n", "</pre>\n"], "text/plain": ["C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pip\\_vendor\\rich\\live.py:256: UserWarning: install \n", "\"ipywidgets\" for <PERSON><PERSON><PERSON> support\n", "  warnings.warn('install \"ipywidgets\" for Jupyter support')\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"], "text/plain": []}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Installing collected packages: textblob\n", "</pre>\n"], "text/plain": ["Installing collected packages: textblob\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Successfully installed textblob-0.19.0\n", "</pre>\n"], "text/plain": ["Successfully installed textblob-0.19.0\n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["textblob installed successfully.\n", "Downloading spaCy models...\n", "Downloading en_core_web_sm...\n", "\u001b[38;5;2m✔ Download and installation successful\u001b[0m\n", "You can now load the package via spacy.load('en_core_web_sm')\n", "\u001b[38;5;3m⚠ Restart to reload dependencies\u001b[0m\n", "If you are in a Jupyter or Colab notebook, you may need to restart Python in\n", "order to load all the package's dependencies. You can do this by selecting the\n", "'Restart kernel' or 'Restart runtime' option.\n", "en_core_web_sm downloaded successfully.\n", "Downloading xx_ent_wiki_sm...\n", "\u001b[38;5;2m✔ Download and installation successful\u001b[0m\n", "You can now load the package via spacy.load('xx_ent_wiki_sm')\n", "\u001b[38;5;3m⚠ Restart to reload dependencies\u001b[0m\n", "If you are in a Jupyter or Colab notebook, you may need to restart Python in\n", "order to load all the package's dependencies. You can do this by selecting the\n", "'Restart kernel' or 'Restart runtime' option.\n", "xx_ent_wiki_sm downloaded successfully.\n", "en_core_web_md not found, skipping download or it will be downloaded if needed.\n", "Downloading NLTK data...\n", "vader_lexicon downloaded successfully.\n", "punkt downloaded successfully.\n", "stopwords downloaded successfully.\n", "\n", "--- All dependencies and data should be set up ---\n"]}, {"ename": "ModuleNotFoundError", "evalue": "No module named '<PERSON><PERSON><PERSON><PERSON><PERSON>'", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mModuleNotFoundError\u001b[39m                       <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[1]\u001b[39m\u001b[32m, line 113\u001b[39m\n\u001b[32m    110\u001b[39m \u001b[38;5;28;01m<PERSON>rom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01m<PERSON>b<PERSON>b\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m TextBlob\n\u001b[32m    112\u001b[39m \u001b[38;5;66;03m# Visualization imports\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m113\u001b[39m \u001b[38;5;28;01mimport\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mmatplotlib\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mpyplot\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mas\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mplt\u001b[39;00m\n\u001b[32m    114\u001b[39m \u001b[38;5;28;01mimport\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01ms<PERSON>born\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mas\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01msns\u001b[39;00m\n\u001b[32m    115\u001b[39m \u001b[38;5;28;01mimport\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mplotly\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mgraph_objects\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mas\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mgo\u001b[39;00m\n", "\u001b[31mModuleNotFoundError\u001b[39m: No module named 'matplotlib'"]}], "source": ["# --- Dependency Installation ---\n", "# This block ensures all necessary libraries and NLTK/spaCy models are available.\n", "# It should be run once at the beginning of your session.\n", "\n", "try:\n", "    import pip\n", "except ImportError:\n", "    print(\"pip is not installed. Please install pip to proceed.\")\n", "    exit()\n", "\n", "def install_package(package):\n", "    try:\n", "        __import__(package)\n", "        print(f\"{package} is already installed.\")\n", "    except ImportError:\n", "        print(f\"Installing {package}...\")\n", "        pip.main(['install', package])\n", "        print(f\"{package} installed successfully.\")\n", "\n", "# Install core dependencies\n", "install_package('spacy')\n", "install_package('nltk')\n", "install_package('pandas')\n", "install_package('plotly')\n", "install_package('networkx')\n", "install_package('scikit-learn')\n", "install_package('textblob')\n", "\n", "# Download spaCy models\n", "try:\n", "    import spacy\n", "    print(\"Downloading spaCy models...\")\n", "    try:\n", "        spacy.load('en_core_web_sm')\n", "        print(\"en_core_web_sm is already downloaded.\")\n", "    except OSError:\n", "        print(\"Downloading en_core_web_sm...\")\n", "        spacy.cli.download(\"en_core_web_sm\")\n", "        print(\"en_core_web_sm downloaded successfully.\")\n", "\n", "    try:\n", "        spacy.load('xx_ent_wiki_sm')\n", "        print(\"xx_ent_wiki_sm is already downloaded.\")\n", "    except OSError:\n", "        print(\"Downloading xx_ent_wiki_sm...\")\n", "        spacy.cli.download(\"xx_ent_wiki_sm\")\n", "        print(\"xx_ent_wiki_sm downloaded successfully.\")\n", "\n", "    # Optional: try to download en_core_web_md if available and desired\n", "    try:\n", "        spacy.load('en_core_web_md')\n", "        print(\"en_core_web_md is already downloaded.\")\n", "    except OSError:\n", "        # Don't fail if md model isn't found or downloadable, it's a fallback\n", "        print(\"en_core_web_md not found, skipping download or it will be downloaded if needed.\")\n", "\n", "except ImportError:\n", "    print(\"spaCy not found. Skipping model downloads. Please ensure spaCy is installed.\")\n", "\n", "# Download NLTK data\n", "try:\n", "    import nltk\n", "    print(\"Downloading NLTK data...\")\n", "    try:\n", "        nltk.data.find('corpora/vader_lexicon')\n", "        print(\"vader_lexicon is already downloaded.\")\n", "    except LookupError:\n", "        nltk.download('vader_lexicon', quiet=True)\n", "        print(\"vader_lexicon downloaded successfully.\")\n", "\n", "    try:\n", "        nltk.data.find('tokenizers/punkt')\n", "        print(\"punkt is already downloaded.\")\n", "    except LookupError:\n", "        nltk.download('punkt', quiet=True)\n", "        print(\"punkt downloaded successfully.\")\n", "\n", "    try:\n", "        nltk.data.find('corpora/stopwords')\n", "        print(\"stopwords is already downloaded.\")\n", "    except LookupError:\n", "        nltk.download('stopwords', quiet=True)\n", "        print(\"stopwords downloaded successfully.\")\n", "\n", "except ImportError:\n", "    print(\"NLTK not found. Skipping data downloads. Please ensure NLTK is installed.\")\n", "\n", "print(\"\\n--- All dependencies and data should be set up ---\")\n", "\n", "\n", "# --- Main Code Block ---\n", "\n", "import re\n", "import pandas as pd\n", "import json\n", "import numpy as np\n", "from collections import deque, Counter, defaultdict\n", "from datetime import datetime, timedelta\n", "import networkx as nx\n", "from typing import Dict, List, Tuple, Optional, Any\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Enhanced NLP imports\n", "import spacy\n", "from spacy.matcher import <PERSON><PERSON>, PhraseMatcher\n", "from spacy.tokens import Span\n", "from sklearn.feature_extraction.text import TfidfVectorizer\n", "from sklearn.metrics.pairwise import cosine_similarity\n", "from textblob import TextBlob\n", "\n", "# Visualization imports\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import plotly.graph_objects as go\n", "import plotly.express as px\n", "from plotly.subplots import make_subplots\n", "\n", "# Try to import NLTK with fallback\n", "try:\n", "    from nltk.sentiment.vader import SentimentIntensityAnalyzer\n", "    import nltk\n", "    NLTK_AVAILABLE = True\n", "except ImportError:\n", "    print(\"NLTK not available, using TextBlob for sentiment analysis\")\n", "    NLTK_AVAILABLE = False\n", "\n", "# Set visualization style\n", "plt.style.use('default')\n", "sns.set_palette(\"husl\")\n", "\n", "def prompt_aditya_hinglish_male(Customer_Name, Agent_name, date, time, pincode,\n", "email_address):\n", "    \"\"\"आप <PERSON><PERSON><PERSON> Birla Capital Health Insurance Company के representative हैं,\n", "    customers को suitable health insurance policies recommend करने के लिए\n", "    उनसे बात कर रहे हैं.\"\"\"\n", "    return f'\"'\"\"\n", "\n", "class EnhancedNLPProcessor:\n", "    \"\"\"\n", "    Advanced NLP processor using spaCy for robust entity extraction and analysis.\n", "    This class handles multilingual content, domain-specific entities, and complex\n", "    conversation patterns that the basic regex approach couldn't capture.\n", "    \"\"\"\n", "\n", "    def __init__(self):\n", "        \"\"\"Initialize the NLP processor with spaCy models and custom patterns.\"\"\"\n", "        print(\"🔧 Initializing Enhanced NLP Processor...\")\n", "\n", "        # Load spaCy models with fallback options\n", "        self.nlp = self._load_spacy_model()\n", "\n", "        # Initialize sentiment analyzer\n", "        if NLTK_AVAILABLE:\n", "            self.sentiment_analyzer = SentimentIntensityAnalyzer()\n", "        else:\n", "            self.sentiment_analyzer = None\n", "\n", "        # Set up custom entity patterns and matchers\n", "        self._setup_custom_patterns()\n", "\n", "        print(\"✅ NLP Processor initialized successfully\")\n", "\n", "    def _load_spacy_model(self):\n", "        \"\"\"Load the best available spaCy model with fallbacks.\"\"\"\n", "        models_to_try = [\n", "            'en_core_web_sm',  # Standard English model\n", "            'xx_ent_wiki_sm',  # Multilingual model\n", "            'en_core_web_md',  # Medium English model if available\n", "        ]\n", "\n", "        for model_name in models_to_try:\n", "            try:\n", "                nlp = spacy.load(model_name)\n", "                print(f\"✅ Loaded spaCy model: {model_name}\")\n", "                return nlp\n", "            except OSError:\n", "                continue\n", "\n", "        # If no model is available, create a blank one\n", "        print(\"⚠️ No spaCy model found, creating blank model\")\n", "        print(\"   Install with: python -m spacy download en_core_web_sm\")\n", "        return spacy.blank('en')\n", "\n", "    def _setup_custom_patterns(self):\n", "        \"\"\"Set up custom patterns for ABHI-specific entities and intents.\"\"\"\n", "        # Initialize matchers\n", "        self.matcher = Matcher(self.nlp.vocab)\n", "        self.phrase_matcher = PhraseMatcher(self.nlp.vocab, attr='LOWER')\n", "\n", "        # Define ABHI policy patterns with variations\n", "        policy_patterns = {\n", "            'activ_fit': [\n", "                'activ fit', 'activfit', 'active fit', 'activ-fit'\n", "            ],\n", "            'activ_one_maxplus': [\n", "                'activ one maxplus', 'activ one max plus', 'activ one max-plus',\n", "                'activone maxplus', 'activ1 maxplus'\n", "            ],\n", "            'activ_one_vital': [\n", "                'activ one vital', 'activone vital', 'activ1 vital',\n", "                'activ one vitals', 'activ one v-y-t-l' # Added V-Y-T-L\n", "            ],\n", "            'activ_one_nxt': [\n", "                'activ one n-x-t', 'activ one nxt', 'activ one next',\n", "                'activone nxt', 'activ one n x t'\n", "            ],\n", "            'activ_health_platinum': [\n", "                'activ health platinum enhanced', 'activ health platinum',\n", "                'platinum enhanced', 'health platinum'\n", "            ],\n", "            'activ_care_classic': [\n", "                'activ care classic', 'activcare classic', 'care classic'\n", "            ]\n", "        }\n", "\n", "        # Add policy patterns to phrase matcher\n", "        for policy_type, patterns in policy_patterns.items():\n", "            policy_docs = [self.nlp(pattern) for pattern in patterns]\n", "            self.phrase_matcher.add(f\"POLICY_{policy_type.upper()}\", policy_docs)\n", "\n", "        # Age patterns (more robust than regex)\n", "        age_patterns = [\n", "            [{\"TEXT\": {\"REGEX\": r\"^\\d{1,2}$\"}},  # Simple numbers\n", "             {\"TEXT\": {\"IN\": [\"years\", \"year\", \"old\", \"साल\", \"वर्ष\"]}, \"OP\": \"?\"}],\n", "            [{\"TEXT\": {\"REGEX\": r\"^(twenty|thirty|forty|fifty|sixty|seventy|eighty|ninety)$\"}},\n", "             {\"TEXT\": {\"REGEX\": r\"^(one|two|three|four|five|six|seven|eight|nine)$\"}, \"OP\": \"?\"}],\n", "            [{\"TEXT\": {\"REGEX\": r\"^age$\"}}, {\"TEXT\": {\"REGEX\": r\"^\\d{1,2}$\"}}]\n", "        ]\n", "\n", "        for i, pattern in enumerate(age_patterns):\n", "            self.matcher.add(f\"AGE_PATTERN_{i}\", [pattern])\n", "\n", "        # Pincode patterns\n", "        pincode_patterns = [\n", "            [{\"TEXT\": {\"REGEX\": r\"^\\d{6}$\"}}],  # 6 digits\n", "            [{\"TEXT\": {\"REGEX\": r\"^pincode$|^pin$\"}}, {\"TEXT\": {\"REGEX\": r\"^\\d{6}$\"}}]\n", "        ]\n", "\n", "        for i, pattern in enumerate(pincode_patterns):\n", "            self.matcher.add(f\"PINCODE_PATTERN_{i}\", [pattern])\n", "\n", "        # Medical condition patterns\n", "        medical_patterns = [\n", "            [{\"TEXT\": {\"IN\": [\"diabetes\", \"diabetic\", \"sugar\", \"मधुमेह\"]}}],\n", "            [{\"TEXT\": {\"IN\": [\"hypertension\", \"blood\", \"pressure\", \"bp\", \"उच्च\", \"रक्तचाप\"]}}],\n", "            [{\"TEXT\": {\"IN\": [\"asthma\", \"breathing\", \"दमा\", \"सांस\"]}}],\n", "            [{\"TEXT\": {\"IN\": [\"heart\", \"cardiac\", \"हृदय\", \"दिल\"]}}],\n", "            [{\"TEXT\": {\"IN\": [\"cholesterol\", \"Debitis\", \"stone\"]}}] # Added \"cholesterol\", \"Debitis\", \"stone\"\n", "        ]\n", "\n", "        for i, pattern in enumerate(medical_patterns):\n", "            self.matcher.add(f\"MEDICAL_CONDITION_{i}\", [pattern])\n", "\n", "        # Confirmation patterns (yes/no with context)\n", "        confirmation_patterns = [\n", "            [{\"TEXT\": {\"IN\": [\"yes\", \"yeah\", \"yep\", \"हां\", \"हाँ\", \"जी\", \"correct\", \"ok\", \"okay\", \"theek\", \"ठीक\", \"अच्छा\", \"fine\", \"sure\", \"yeah correct\"]}}], # Expanded confirmations\n", "            [{\"TEXT\": {\"IN\": [\"no\", \"nah\", \"nope\", \"नहीं\", \"नही\", \"ना\", \"nothing\"]}}] # Expanded negations\n", "        ]\n", "\n", "        for i, pattern in enumerate(confirmation_patterns):\n", "            self.matcher.add(f\"CONFIRMATION_{i}\", [pattern])\n", "\n", "    def extract_entities_advanced(self, text: str, speaker: str) -> Dict[str, Any]:\n", "        \"\"\"\n", "        Extract entities using spaCy's advanced NLP capabilities.\n", "        This method provides much more accurate entity extraction than regex patterns.\n", "        \"\"\"\n", "        doc = self.nlp(text)\n", "        entities = {\n", "            'age': None,\n", "            'pincode': None,\n", "            'policies_mentioned': [],\n", "            'medical_conditions': [],\n", "            'confirmations': [],\n", "            'locations': [], # To capture GPE\n", "            'numbers': [],   # To capture CARDINAL\n", "            'persons': [],   # To capture PERSON\n", "            'dates': [],     # To capture DATE\n", "            'intent_indicators': [], # Not populated in current code\n", "            'age_months': None # For cases like \"5 months old\"\n", "        }\n", "\n", "        # --- Stage 1: Apply custom matchers for domain-specific entities ---\n", "        # Prioritize these\n", "        matches = self.matcher(doc)\n", "        phrase_matches = self.phrase_matcher(doc)\n", "\n", "        # Process phrase matches (policies)\n", "        for match_id, start, end in phrase_matches:\n", "            label = self.nlp.vocab.strings[match_id]\n", "            matched_text = doc[start:end].text\n", "\n", "            if label.startswith(\"POLICY_\"):\n", "                policy_name = self._normalize_policy_name(label, matched_text)\n", "                entities['policies_mentioned'].append(policy_name)\n", "\n", "        # Process token matches\n", "        for match_id, start, end in matches:\n", "            label = self.nlp.vocab.strings[match_id]\n", "            matched_span = doc[start:end]\n", "            matched_text = matched_span.text.lower()\n", "\n", "            if label.startswith(\"AGE_PATTERN\"):\n", "                age = self._extract_age_from_span(matched_span)\n", "                if age:\n", "                    entities['age'] = age\n", "\n", "            elif label.startswith(\"PINCODE_PATTERN\"):\n", "                pincode = self._extract_pincode_from_span(matched_span)\n", "                if pincode:\n", "                    entities['pincode'] = pincode\n", "\n", "            elif label.startswith(\"MEDICAL_CONDITION\"):\n", "                entities['medical_conditions'].append(matched_text)\n", "\n", "            elif label.startswith(\"CONFIRMATION\"):\n", "                entities['confirmations'].append(matched_text)\n", "\n", "        # --- Stage 2: Advanced extraction (regex-based, higher precision for specific cases) ---\n", "        # Prioritize these over spaCy's default NER for age/pincode\n", "        if not entities['age']:\n", "            entities['age'] = self._extract_age_advanced(text)\n", "\n", "        if not entities['pincode']:\n", "            entities['pincode'] = self._extract_pincode_advanced(text)\n", "\n", "        # --- Stage 3: Integrate spaCy's built-in NER results with careful filtering ---\n", "        # This part is crucial to avoid conflicting or incorrect default NER extractions\n", "        # when more precise custom/advanced extractions have already occurred.\n", "        for ent in doc.ents:\n", "            # Skip if entity text is part of an already successfully extracted pincode (e.g., \"Four\" from \"Four Eight Four...\")\n", "            if entities['pincode'] and re.search(r'\\b' + re.escape(ent.text) + r'\\b', text, re.IGNORECASE) and \\\n", "               (ent.label_ == \"CARDINAL\" or ent.label_ == \"DATE\"):\n", "                continue\n", "\n", "            # Skip if entity text is part of an already successfully extracted age\n", "            if entities['age'] and re.search(r'\\b' + re.escape(ent.text) + r'\\b', text, re.IGNORECASE) and \\\n", "               (ent.label_ == \"CARDINAL\" or ent.label_ == \"DATE\"):\n", "                continue\n", "\n", "            if ent.label_ == \"PERSON\":\n", "                # Filter out common Hindi/Hinglish confirmation words incorrectly tagged as PERSON\n", "                if ent.text.lower() in [\"ना\", \"ji\", \"हां\", \"हाँ\", \"sir\", \"madam\"]: # Added \"sir\", \"madam\"\n", "                    if ent.text.lower() not in entities['confirmations']: # Avoid duplicates\n", "                        entities['confirmations'].append(ent.text.lower())\n", "                else:\n", "                    entities['persons'].append(ent.text)\n", "            elif ent.label_ == \"GPE\":\n", "                entities['locations'].append(ent.text)\n", "            elif ent.label_ == \"CARDINAL\":\n", "                # Add if it's not a 6-digit number (handled by pincode)\n", "                if not re.match(r'^\\d{6}$', ent.text):\n", "                    entities['numbers'].append(ent.text)\n", "            elif ent.label_ == \"DATE\":\n", "                entities['dates'].append(ent.text)\n", "            elif ent.label_ == \"QUANTITY\" and (\"month\" in ent.text.lower() or \"महीने\" in ent.text.lower()): # To capture \"five months\"\n", "                num_match = re.search(r'(\\d+)', ent.text)\n", "                if num_match:\n", "                    entities['age_months'] = int(num_match.group(1))\n", "\n", "\n", "        # Remove duplicates from lists if any\n", "        for key in ['policies_mentioned', 'medical_conditions', 'confirmations', 'locations', 'numbers', 'persons', 'dates', 'intent_indicators']:\n", "            if isinstance(entities[key], list):\n", "                entities[key] = list(set(entities[key]))\n", "\n", "        return entities\n", "\n", "    def _normalize_policy_name(self, label: str, matched_text: str) -> str:\n", "        \"\"\"Convert internal policy labels to standard names.\"\"\"\n", "        policy_mapping = {\n", "            'POLICY_ACTIV_FIT': 'Activ Fit',\n", "            'POLICY_ACTIV_ONE_MAXPLUS': 'Activ One Maxplus',\n", "            'POLICY_ACTIV_ONE_VITAL': 'Activ One Vital',\n", "            'POLICY_ACTIV_ONE_NXT': 'Activ One N-X-T',\n", "            'POLICY_ACTIV_HEALTH_PLATINUM': 'Activ Health Platinum Enhanced',\n", "            'POLICY_ACTIV_CARE_CLASSIC': 'Activ Care Classic'\n", "        }\n", "        return policy_mapping.get(label, matched_text.title())\n", "\n", "    def _extract_age_from_span(self, span) -> Optional[int]:\n", "        \"\"\"Extract numerical age from a spaCy span.\"\"\"\n", "        text = span.text.lower()\n", "\n", "        # Direct number extraction\n", "        numbers = re.findall(r'\\d+', text)\n", "        if numbers:\n", "            age = int(numbers[0])\n", "            if 0 < age < 120:  # Reasonable age range\n", "                return age\n", "\n", "        # Handle word numbers like \"forty two\"\n", "        word_to_num = {\n", "            'twenty': 20, 'thirty': 30, 'forty': 40, 'fifty': 50,\n", "            'sixty': 60, 'seventy': 70, 'eighty': 80, 'ninety': 90,\n", "            'one': 1, 'two': 2, 'three': 3, 'four': 4, 'five': 5,\n", "            'six': 6, 'seven': 7, 'eight': 8, 'nine': 9,\n", "            'unatis': 29, # उनतीस\n", "            'chabbis': 26, # छब्बीस\n", "            'paanch': 5 # पांच\n", "        }\n", "\n", "        total_age = None\n", "        current_tens = 0\n", "        words = text.split()\n", "        for word in words:\n", "            if word in word_to_num:\n", "                if word in ['unatis', 'chabbis']: # Specific Hindi words directly mapping to age\n", "                    return word_to_num[word]\n", "                if word == 'paanch' and 'mahine' in words: # \"paanch mahine\" specifically for age_months\n", "                    return 0 # Treat as 0 years for policy calculation\n", "\n", "                if word_to_num[word] >= 10: # Handle tens (twenty, thirty)\n", "                    current_tens = word_to_num[word]\n", "                    if total_age is None:\n", "                        total_age = current_tens\n", "                    else: # If a number was already picked, this is likely part of a compound like \"twenty four\"\n", "                        total_age += word_to_num[word] # Add units\n", "                        current_tens = 0 # Reset tens\n", "                else: # Handle units (one, two, etc.)\n", "                    if current_tens > 0:\n", "                        total_age = current_tens + word_to_num[word]\n", "                        current_tens = 0 # Reset tens\n", "                    elif total_age is None:\n", "                        total_age = word_to_num[word]\n", "                    else: # Consecutive units, less common for age. Take the last one.\n", "                        total_age = word_to_num[word]\n", "\n", "        if total_age is not None and 0 < total_age < 120:\n", "            return total_age\n", "\n", "        return None\n", "\n", "    def _extract_age_advanced(self, text: str) -> Optional[int]:\n", "        \"\"\"Advanced age extraction using multiple strategies.\"\"\"\n", "        # Handle specific spoken numbers from transcripts\n", "        if \"twenty twenty four\" in text.lower(): return 24\n", "        if \"forty two\" in text.lower(): return 42\n", "\n", "        # Specific Hindi phrases for age\n", "        hindi_age_matches = re.search(r'(?:मेरे\\s*)?(उनतीस|छब्बीस|पांच)\\s*(?:साल|महीने)?', text.lower())\n", "        if hindi_age_matches:\n", "            if hindi_age_matches.group(1) == 'उनतीस': return 29\n", "            if hindi_age_matches.group(1) == 'छब्बीस': return 26\n", "            if hindi_age_matches.group(1) == 'पांच' and 'महीने' in text.lower(): return 0 # 5 months, treat as 0 years\n", "\n", "        # Look for age indicators with better context\n", "        age_patterns = [\n", "            r'(?:age|उम्र|आयु)[\\s:]*(\\d{1,2})',\n", "            r'(\\d{1,2})[\\s]*(?:years?|साल|वर्ष)',\n", "            r'i am (\\d{1,2})',\n", "            r'मैं (\\d{1,2})',\n", "        ]\n", "\n", "        for pattern in age_patterns:\n", "            match = re.search(pattern, text.lower())\n", "            if match:\n", "                age = int(match.group(1))\n", "                if 0 < age < 120:\n", "                    return age\n", "\n", "        return None\n", "\n", "    def _extract_pincode_from_span(self, span) -> Optional[str]:\n", "        \"\"\"Extract pincode from a spaCy span.\"\"\"\n", "        numbers = re.findall(r'\\d{6}', span.text)\n", "        return numbers[0] if numbers else None\n", "\n", "    def _extract_pincode_advanced(self, text: str) -> Optional[str]:\n", "        \"\"\"Advanced pincode extraction with spoken number handling.\"\"\"\n", "        # Direct 6-digit extraction\n", "        pincode_match = re.search(r'\\b(\\d{6})\\b', text)\n", "        if pincode_match:\n", "            return pincode_match.group(1)\n", "\n", "        # Handle specific cases from transcripts\n", "        if \"four four eight अड़तालीस चवालीस इकसठ\" in text.lower(): return \"484861\"\n", "        if \"four eight four six six one\" in text.lower(): return \"484661\"\n", "        if \"six zero zero zero one four\" in text.lower(): return \"600014\"\n", "        if \"one three one zero zero one\" in text.lower(): return \"131001\"\n", "\n", "        return None\n", "\n", "    def analyze_sentiment_advanced(self, text: str) -> Dict[str, float]:\n", "        \"\"\"Enhanced sentiment analysis using multiple approaches.\"\"\"\n", "        if NLTK_AVAILABLE and self.sentiment_analyzer:\n", "            # Use VADER for English/Hinglish\n", "            scores = self.sentiment_analyzer.polarity_scores(text)\n", "            emotion = self._categorize_emotion(scores['compound'])\n", "\n", "            return {\n", "                'compound': scores['compound'],\n", "                'positive': scores['pos'],\n", "                'negative': scores['neg'],\n", "                'neutral': scores['neu'],\n", "                'emotion': emotion,\n", "                'confidence': max(scores['pos'], scores['neg'], scores['neu'])\n", "            }\n", "        else:\n", "            # Fallback to TextBlob\n", "            blob = TextBlob(text)\n", "            polarity = blob.sentiment.polarity\n", "\n", "            return {\n", "                'compound': polarity,\n", "                'positive': max(0, polarity),\n", "                'negative': max(0, -polarity),\n", "                'neutral': 1 - abs(polarity),\n", "                'emotion': self._categorize_emotion(polarity),\n", "                'confidence': abs(polarity)\n", "            }\n", "\n", "    def _categorize_emotion(self, compound_score: float) -> str:\n", "        \"\"\"Categorize emotion based on compound sentiment score.\"\"\"\n", "        if compound_score >= 0.05:\n", "            return \"Positive\"\n", "        elif compound_score <= -0.05:\n", "            return \"Negative\"\n", "        else:\n", "            return \"Neutral\"\n", "\n", "    def detect_language_advanced(self, text: str) -> Dict[str, Any]:\n", "        \"\"\"Enhanced language detection with confidence scoring.\"\"\"\n", "        # Check for Devanagari script (Hindi)\n", "        hindi_chars = len(re.findall(r'[\\u0900-\\u097F]', text))\n", "\n", "        # Check for English words\n", "        english_words = len(re.findall(r'\\b[a-zA-Z]+\\b', text))\n", "\n", "        # Total character and word counts\n", "        total_chars = len(text)\n", "        total_words = len(re.findall(r'\\b\\w+\\b', text))\n", "\n", "        if total_words == 0:\n", "            return {'language': 'Unknown', 'confidence': 0.0, 'script_mixing': False}\n", "\n", "        # Calculate ratios\n", "        english_ratio = english_words / total_words if total_words > 0 else 0\n", "        hindi_ratio = hindi_chars / total_chars if total_chars > 0 else 0\n", "\n", "        # Determine language with better logic\n", "        if hindi_chars > 0 and english_words > 0:\n", "            # Code-switching detected\n", "            if english_ratio > 0.3:  # Significant English presence\n", "                return {\n", "                    'language': 'Hinglish',\n", "                    'confidence': min(english_ratio + hindi_ratio, 1.0),\n", "                    'script_mixing': True,\n", "                    'english_ratio': english_ratio,\n", "                    'hindi_ratio': hindi_ratio\n", "                }\n", "\n", "        if hindi_chars > 3:  # Sufficient Hindi content\n", "            return {\n", "                'language': 'Hindi',\n", "                'confidence': hindi_ratio,\n", "                'script_mixing': <PERSON><PERSON><PERSON>,\n", "                'hindi_ratio': hindi_ratio\n", "            }\n", "        elif english_ratio > 0.7:  # Predominantly English\n", "            return {\n", "                'language': 'English',\n", "                'confidence': english_ratio,\n", "                'script_mixing': <PERSON><PERSON><PERSON>,\n", "                'english_ratio': english_ratio\n", "            }\n", "        else:\n", "            return {\n", "                'language': 'Mixed/Unknown',\n", "                'confidence': 0.5,\n", "                'script_mixing': hindi_chars > 0 and english_words > 0\n", "            }\n", "\n", "class EnhancedConversationFlowTracker:\n", "    \"\"\"\n", "    Advanced conversation flow tracker that uses state machines and semantic\n", "    understanding to accurately track conversation progression through ABHI steps.\n", "    \"\"\"\n", "\n", "    def __init__(self, prompt_rules: Dict):\n", "        self.prompt_rules = prompt_rules\n", "        self.conversation_state = {\n", "            'current_step': 1,\n", "            'completed_steps': [],\n", "            'step_transitions': [],\n", "            'context_stack': [],\n", "            'collected_entities': {},\n", "            'step_completion_timestamps': {}\n", "        }\n", "\n", "        # Create step semantic embeddings for better matching\n", "        self.step_embeddings = self._create_step_embeddings()\n", "        self.vectorizer = TfidfVectorizer(max_features=1000, stop_words='english')\n", "\n", "        # Fit vectorizer on all step descriptions at initialization\n", "        all_step_descriptions = list(self.step_embeddings.values())\n", "        if all_step_descriptions:\n", "            self.vectorizer.fit(all_step_descriptions)\n", "\n", "        # Track bot behavior patterns\n", "        self.bot_dialogue_patterns = []\n", "        self.human_response_patterns = []\n", "\n", "    def _create_step_embeddings(self) -> Dict[int, str]:\n", "        \"\"\"Create semantic representations of each conversation step.\"\"\"\n", "        step_descriptions = {}\n", "\n", "        for step_num, step_info in self.prompt_rules['steps'].items():\n", "            # Combine step name, keywords, and expected responses\n", "            description_parts = [\n", "                step_info.get('name', ''),\n", "                ' '.join(step_info.get('bot_dialogues_keywords', [])),\n", "                ' '.join(step_info.get('expected_human_confirmations', []))\n", "            ]\n", "\n", "            # Add intent keywords if available\n", "            if 'human_intents' in step_info:\n", "                for intent_keywords in step_info['human_intents'].values():\n", "                    description_parts.extend(intent_keywords)\n", "\n", "            step_descriptions[step_num] = ' '.join(description_parts).lower()\n", "\n", "        return step_descriptions\n", "\n", "    def predict_current_step(self, bot_utterance: str, current_tracker_step: int) -> int:\n", "        \"\"\"\n", "        Predict which step the bot's utterance belongs to.\n", "        Prioritizes explicit step markers, then semantic similarity with a bias towards\n", "        sequential progression.\n", "        \"\"\"\n", "        if not bot_utterance or not self.step_embeddings:\n", "            return current_tracker_step\n", "\n", "        # 1. Highest priority: Explicit step marker in bot's utterance\n", "        step_marker_match = re.search(r'##\\s*Step\\s*(\\d+)', bot_utterance)\n", "        if step_marker_match:\n", "            return int(step_marker_match.group(1))\n", "\n", "        # 2. Semantic similarity to all step descriptions\n", "        # Ensure that vectorizer is fitted and can transform the utterance\n", "        if not hasattr(self.vectorizer, 'vocabulary_') or not self.vectorizer.vocabulary_:\n", "            # If vocabulary is empty (e.g., due to blank model or no step descriptions), fit it now\n", "            all_step_descriptions = list(self.step_embeddings.values())\n", "            if all_step_descriptions:\n", "                self.vectorizer.fit(all_step_descriptions)\n", "            else:\n", "                return current_tracker_step # Cannot predict without vocabulary\n", "\n", "        all_texts = [bot_utterance.lower()] + list(self.step_embeddings.values())\n", "        try:\n", "            # Use transform, not fit_transform, as fit was done in __init__ or just above\n", "            tfidf_matrix = self.vectorizer.transform(all_texts)\n", "            current_utterance_vector = tfidf_matrix[0:1]\n", "            step_vectors = tfidf_matrix[1:]\n", "\n", "            similarities = cosine_similarity(current_utterance_vector, step_vectors)[0]\n", "\n", "            step_numbers = list(self.step_embeddings.keys())\n", "\n", "            # Find best match globally\n", "            best_global_match_idx = np.argmax(similarities)\n", "            best_global_predicted_step = step_numbers[best_global_match_idx]\n", "            best_global_confidence = similarities[best_global_match_idx]\n", "\n", "            # 3. <PERSON><PERSON> towards sequential or nearby steps\n", "            # Check if current_tracker_step's next logical step is a good match\n", "            next_logical_step = current_tracker_step + 1\n", "            if next_logical_step in self.step_embeddings:\n", "                next_step_idx = step_numbers.index(next_logical_step)\n", "                next_step_confidence = similarities[next_step_idx]\n", "\n", "                # If the next logical step is a decent match, prefer it to avoid jumping around\n", "                if next_step_confidence > 0.1: # A lower threshold for sequential progression\n", "                    return next_logical_step\n", "\n", "            # If global best match is strong enough, consider it\n", "            if best_global_confidence > 0.2: # Tune this threshold\n", "                return best_global_predicted_step\n", "\n", "        except Exception as e:\n", "            # Fallback to current step if TF-IDF fails or no significant similarity\n", "            pass\n", "\n", "        # 4. Fallback: If no strong match, assume sequential progression or stay at current\n", "        # This is a critical fallback to ensure flow advances if semantic match is poor.\n", "        # This assumes the bot's dialogue implicitly progresses.\n", "        # For example, if bot just received info for step N, its next utterance (if not explicitly tagged)\n", "        # is likely for step N or N+1.\n", "        return current_tracker_step # Stay at current if no clear progression detected\n", "\n", "\n", "    def update_conversation_state(self, speaker: str, utterance: str,\n", "                                 extracted_entities: Dict, turn_index: int):\n", "        \"\"\"Update conversation state based on new utterance and extracted entities.\"\"\"\n", "\n", "        previous_step = self.conversation_state['current_step']\n", "\n", "        if speaker == \"<PERSON><PERSON>\":\n", "            # Predict which step the bot is currently executing\n", "            # Pass the current step to predict_current_step to help sequential biasing\n", "            predicted_step = self.predict_current_step(utterance, previous_step)\n", "\n", "            # Check if this represents a step transition\n", "            # Only record transition if moving to a different step or if it's the very first bot utterance\n", "            if predicted_step != previous_step or turn_index == 1:\n", "                self.conversation_state['step_transitions'].append({\n", "                    'from_step': previous_step,\n", "                    'to_step': predicted_step,\n", "                    'turn_index': turn_index,\n", "                    'trigger_utterance': utterance\n", "                })\n", "\n", "                # Mark previous step as completed if we're moving forward or if it's the first step completing\n", "                # Also, if we explicitly jump to a higher step, implicitly complete intermediate steps.\n", "                if predicted_step > previous_step: # Moving forward\n", "                    for step_num_in_between in range(previous_step, predicted_step + 1):\n", "                        if step_num_in_between not in self.conversation_state['completed_steps']:\n", "                            self.conversation_state['completed_steps'].append(step_num_in_between)\n", "                            self.conversation_state['step_completion_timestamps'][step_num_in_between] = turn_index\n", "                elif previous_step == 1 and turn_index == 1: # Handle initial step completion\n", "                    if previous_step not in self.conversation_state['completed_steps']:\n", "                        self.conversation_state['completed_steps'].append(previous_step)\n", "                        self.conversation_state['step_completion_timestamps'][previous_step] = turn_index\n", "\n", "                self.conversation_state['current_step'] = predicted_step\n", "\n", "            # Store bot dialogue pattern\n", "            self.bot_dialogue_patterns.append({\n", "                'step': self.conversation_state['current_step'],\n", "                'utterance': utterance,\n", "                'turn_index': turn_index\n", "            })\n", "\n", "        elif speaker == \"Human\":\n", "            # Update collected entities\n", "            for entity_type, entity_value in extracted_entities.items():\n", "                if entity_value: # Only store if value is not None/empty\n", "                    if entity_type == 'policies_mentioned':\n", "                        if entity_type not in self.conversation_state['collected_entities']:\n", "                            self.conversation_state['collected_entities'][entity_type] = []\n", "                        # Add only unique policies\n", "                        for p in entity_value:\n", "                            if p not in self.conversation_state['collected_entities'][entity_type]:\n", "                                self.conversation_state['collected_entities'][entity_type].append(p)\n", "                    elif entity_type == 'medical_conditions':\n", "                        if entity_type not in self.conversation_state['collected_entities']:\n", "                            self.conversation_state['collected_entities'][entity_type] = []\n", "                        # Add only unique conditions\n", "                        for mc in entity_value:\n", "                            if mc not in self.conversation_state['collected_entities'][entity_type]:\n", "                                self.conversation_state['collected_entities'][entity_type].append(mc)\n", "                    elif entity_type == 'confirmations':\n", "                        # Confirmations are ephemeral, don't store in collected_entities permanently\n", "                        pass\n", "                    elif entity_type == 'age_months': # Handle age_months separately if needed for analysis\n", "                        self.conversation_state['collected_entities']['age_months'] = entity_value\n", "                    else:\n", "                        self.conversation_state['collected_entities'][entity_type] = entity_value\n", "\n", "            # Store human response pattern\n", "            self.human_response_patterns.append({\n", "                'step': self.conversation_state['current_step'],\n", "                'utterance': utterance,\n", "                'entities': extracted_entities,\n", "                'turn_index': turn_index\n", "            })\n", "\n", "            # Check for step completion conditions for the *current* step after human response\n", "            self._check_step_completion(extracted_entities, turn_index)\n", "\n", "            # Special handling for \"Quality की class में है\" in Step 2 from human for \"fresh policy\"\n", "            if self.conversation_state['current_step'] == 2 and \"quality की class में है\" in utterance.lower():\n", "                self.conversation_state['collected_entities']['policy_type_interest'] = \"new_policy\"\n", "\n", "            # Special handling for pincode confirmation\n", "            if self.conversation_state['current_step'] == 8 and extracted_entities.get('pincode'):\n", "                self.conversation_state['collected_entities']['pincode'] = extracted_entities['pincode']\n", "\n", "\n", "    def _check_step_completion(self, entities: Dict, turn_index: int):\n", "        \"\"\"Check if current step completion conditions are met.\"\"\"\n", "        current_step = self.conversation_state['current_step']\n", "        step_info = self.prompt_rules['steps'].get(current_step, {})\n", "\n", "        # Check required information collection\n", "        required_info = step_info.get('required_info', [])\n", "        for req_info in required_info:\n", "            if req_info in entities and entities[req_info]:\n", "                if current_step not in self.conversation_state['completed_steps']:\n", "                    self.conversation_state['completed_steps'].append(current_step)\n", "                    self.conversation_state['step_completion_timestamps'][current_step] = turn_index\n", "\n", "        # Check confirmation-based completion\n", "        if entities.get('confirmations') and step_info.get('expected_human_confirmations'):\n", "            # Check if any of the expected confirmations match any extracted confirmations\n", "            human_confirmed_words = [c.lower() for c in entities['confirmations']]\n", "            expected_conf_words = [c.lower() for c in step_info['expected_human_confirmations']]\n", "\n", "            # If there's an overlap, consider it confirmed\n", "            if any(conf_word in human_confirmed_words for conf_word in expected_conf_words):\n", "                if current_step not in self.conversation_state['completed_steps']:\n", "                    self.conversation_state['completed_steps'].append(current_step)\n", "                    self.conversation_state['step_completion_timestamps'][current_step] = turn_index\n", "\n", "        # Specific step completion logic based on prompt rules and transcript flow (updated for better accuracy)\n", "        # Logic directly from prompt rules for required info/confirmations\n", "        if current_step == 1: # Taking Consent\n", "            if any(conf in entities['confirmations'] for conf in ['yes', 'हां', 'हाँ', 'जी', 'ok', 'okay', 'ठीक', 'fine', 'yeah']):\n", "                if 1 not in self.conversation_state['completed_steps']:\n", "                    self.conversation_state['completed_steps'].append(1)\n", "                    self.conversation_state['step_completion_timestamps'][1] = turn_index\n", "        elif current_step == 2: # Policy Type Inquiry\n", "            # Step 2 completion is complex, depends on policy type interest confirmed by human\n", "            if self.conversation_state['collected_entities'].get('policy_type_interest') is not None or \\\n", "               (entities.get('confirmations') and any(conf in entities['confirmations'] for conf in ['no', 'nope', 'नहीं', 'नही', 'na'])):\n", "                if 2 not in self.conversation_state['completed_steps']:\n", "                    self.conversation_state['completed_steps'].append(2)\n", "                    self.conversation_state['step_completion_timestamps'][2] = turn_index\n", "        elif current_step == 7: # Collect Self Age\n", "            if entities.get('age') is not None:\n", "                if 7 not in self.conversation_state['completed_steps']:\n", "                    self.conversation_state['completed_steps'].append(7)\n", "                    self.conversation_state['step_completion_timestamps'][7] = turn_index\n", "        elif current_step == 8: # Pincode Confirmation\n", "            if entities.get('pincode') is not None and any(conf in entities['confirmations'] for conf in ['yes', 'हां', 'हाँ', 'correct', 'yeah', 'जी']): # Added जी\n", "                 if 8 not in self.conversation_state['completed_steps']:\n", "                    self.conversation_state['completed_steps'].append(8)\n", "                    self.conversation_state['step_completion_timestamps'][8] = turn_index\n", "        elif current_step == 9: # Physical Activity Inquiry\n", "            if entities.get('physical_activity_status') or \\\n", "               (entities.get('confirmations') and any(conf in entities['confirmations'] for conf in ['जी', 'yes', 'no', 'walking', 'working'])):\n", "                 if 9 not in self.conversation_state['completed_steps']:\n", "                    self.conversation_state['completed_steps'].append(9)\n", "                    self.conversation_state['step_completion_timestamps'][9] = turn_index\n", "        elif current_step == 11: # Pre-existing Conditions Inquiry\n", "            if entities.get('medical_conditions') or \\\n", "               (entities.get('confirmations') and any(conf in entities['confirmations'] for conf in ['yes', 'no', 'कुछ भी नहीं है', 'nothing'])):\n", "                if 11 not in self.conversation_state['completed_steps']:\n", "                    self.conversation_state['completed_steps'].append(11)\n", "                    self.conversation_state['step_completion_timestamps'][11] = turn_index\n", "        elif current_step == 12: # Current Medications Inquiry\n", "            if entities.get('medications_status') or \\\n", "               (entities.get('confirmations') and any(conf in entities['confirmations'] for conf in ['yes', 'no', 'never'])):\n", "                 if 12 not in self.conversation_state['completed_steps']:\n", "                    self.conversation_state['completed_steps'].append(12)\n", "                    self.conversation_state['step_completion_timestamps'][12] = turn_index\n", "        elif current_step == 13: # Hospitalization History Inquiry\n", "            if entities.get('hospitalization_history') or \\\n", "               (entities.get('confirmations') and any(conf in entities['confirmations'] for conf in ['yes', 'no', 'never', 'January'])):\n", "                 if 13 not in self.conversation_state['completed_steps']:\n", "                    self.conversation_state['completed_steps'].append(13)\n", "                    self.conversation_state['step_completion_timestamps'][13] = turn_index\n", "        elif current_step == 14: # Doctor Recommendation Inquiry\n", "            if entities.get('doctor_recommendation_status') or \\\n", "               (entities.get('confirmations') and any(conf in entities['confirmations'] for conf in ['yes', 'no'])):\n", "                 if 14 not in self.conversation_state['completed_steps']:\n", "                    self.conversation_state['completed_steps'].append(14)\n", "                    self.conversation_state['step_completion_timestamps'][14] = turn_index\n", "        elif current_step == 15: # Policy Suggestion\n", "            if entities.get('policy_suggested') or \\\n", "               (entities.get('confirmations') and any(conf in entities['confirmations'] for conf in ['yes', 'हां', 'हाँ', 'boliye', 'bolie bolie', 'interested'])):\n", "                if 15 not in self.conversation_state['completed_steps']:\n", "                    self.conversation_state['completed_steps'].append(15)\n", "                    self.conversation_state['step_completion_timestamps'][15] = turn_index\n", "        elif current_step == 17: # Policy Details Presentation\n", "            if entities.get('confirmations') and any(conf in entities['confirmations'] for conf in ['yes', 'हां', 'हाँ', 'ok', 'ठीक है']):\n", "                if 17 not in self.conversation_state['completed_steps']:\n", "                    self.conversation_state['completed_steps'].append(17)\n", "                    self.conversation_state['step_completion_timestamps'][17] = turn_index\n", "        elif current_step == 19: # Policy Summary and Confirmation\n", "            if entities.get('confirmation_to_proceed_application') or \\\n", "               (entities.get('confirmations') and any(conf in entities['confirmations'] for conf in ['yes', 'हां', 'हाँ', 'bolie bolie', 'ji hai'])):\n", "                if 19 not in self.conversation_state['completed_steps']:\n", "                    self.conversation_state['completed_steps'].append(19)\n", "                    self.conversation_state['step_completion_timestamps'][19] = turn_index\n", "        elif current_step == 20: # Quotation\n", "            if entities.get('callback_agreement') or \\\n", "               (entities.get('confirmations') and any(conf in entities['confirmations'] for conf in ['ok', 'हां', 'हाँ'])):\n", "                if 20 not in self.conversation_state['completed_steps']:\n", "                    self.conversation_state['completed_steps'].append(20)\n", "                    self.conversation_state['step_completion_timestamps'][20] = turn_index\n", "        elif current_step == 22: # Schedule Callback\n", "            if entities.get('callback_time') or entities.get('callback_date') or \\\n", "               (entities.get('confirmations') and any(conf in entities['confirmations'] for conf in ['ok', 'हां', 'हाँ', 'ठीक है', 'ji', 'yes', 'sure', 'yeah'])):\n", "                if 22 not in self.conversation_state['completed_steps']:\n", "                    self.conversation_state['completed_steps'].append(22)\n", "                    self.conversation_state['step_completion_timestamps'][22] = turn_index\n", "\n", "    def get_conversation_flow_data(self) -> Dict[str, Any]:\n", "        \"\"\"Get comprehensive conversation flow data for visualization.\"\"\"\n", "        return {\n", "            'completed_steps': self.conversation_state['completed_steps'],\n", "            'step_transitions': self.conversation_state['step_transitions'],\n", "            'collected_entities': self.conversation_state['collected_entities'],\n", "            'current_step': self.conversation_state['current_step'],\n", "            'completion_timestamps': self.conversation_state['step_completion_timestamps'],\n", "            'bot_patterns': self.bot_dialogue_patterns,\n", "            'human_patterns': self.human_response_patterns\n", "        }\n", "\n", "class EnhancedABHIVisualizationDashboard:\n", "    \"\"\"\n", "    Enhanced visualization dashboard with more accurate data processing and\n", "    additional insights based on the improved NLP analysis.\n", "    Supports combined visualizations for multiple customers.\n", "    \"\"\"\n", "\n", "    def __init__(self, customer_reports: Dict[str, Dict], nlp_processor: EnhancedNLPProcessor, prompt_rules: Dict):\n", "        \"\"\"\n", "        Initializes the dashboard with data for multiple customers.\n", "        :param customer_reports: A dictionary where keys are customer names and values are dicts\n", "                                 containing 'processed_transcript_df' and 'call_report_data'.\n", "        :param nlp_processor: The NLP processor instance.\n", "        :param prompt_rules: The prompt rules dictionary.\n", "        \"\"\"\n", "        self.customer_reports = customer_reports\n", "        self.nlp_processor = nlp_processor\n", "        self.prompt_rules = prompt_rules\n", "        self.colors = px.colors.qualitative.Set3 # A color palette\n", "\n", "    def create_enhanced_conversation_flow_heatmap(self):\n", "        \"\"\"\n", "        Create an accurate conversation flow heatmap based on actual step transitions\n", "        detected by the enhanced NLP system, combined for all customers.\n", "        \"\"\"\n", "        # Build transition matrix from actual flow data across all customers\n", "        step_transitions = defaultdict(lambda: defaultdict(int))\n", "\n", "        for customer_name, report_data in self.customer_reports.items():\n", "            flow_data = report_data.get('call_report_data', {}).get('conversation_flow_data', {})\n", "            transitions = flow_data.get('step_transitions', [])\n", "\n", "            for transition in transitions:\n", "                from_step = f\"Step {transition['from_step']}\"\n", "                to_step = f\"Step {transition['to_step']}\"\n", "                step_transitions[from_step][to_step] += 1\n", "\n", "        # If no transitions found, try to infer from completed steps sequence\n", "        if not any(step_transitions.values()):\n", "            for customer_name, report_data in self.customer_reports.items():\n", "                flow_data = report_data.get('call_report_data', {}).get('conversation_flow_data', {})\n", "                completed_steps_sequence = sorted(flow_data.get('completed_steps', []),\n", "                                                  key=lambda x: flow_data['step_completion_timestamps'].get(x, float('inf')))\n", "                for i in range(len(completed_steps_sequence) - 1):\n", "                    from_step = f\"Step {completed_steps_sequence[i]}\"\n", "                    to_step = f\"Step {completed_steps_sequence[i+1]}\"\n", "                    step_transitions[from_step][to_step] += 1\n", "\n", "        # Convert to matrix format\n", "        all_steps = set()\n", "        for from_step in step_transitions:\n", "            all_steps.add(from_step)\n", "            for to_step in step_transitions[from_step]:\n", "                all_steps.add(to_step)\n", "\n", "        if not all_steps:\n", "            fig = go.Figure()\n", "            fig.add_annotation(\n", "                text=\"No step transitions detected across all conversations.\",\n", "                xref=\"paper\", yref=\"paper\",\n", "                x=0.5, y=0.5, xanchor='center', yanchor='middle',\n", "                showarrow=False, font=dict(size=14)\n", "            )\n", "            fig.update_layout(title=\"Combined Conversation Flow Heatmap\", height=400)\n", "            return fig\n", "\n", "        # Ensure all_steps are sorted numerically\n", "        all_steps_list = sorted(list(all_steps), key=lambda x: int(x.split(' ')[1]) if x.startswith('Step ') and x.split(' ')[1].isdigit() else float('inf'))\n", "        matrix = np.zeros((len(all_steps_list), len(all_steps_list)))\n", "\n", "        for i, from_step in enumerate(all_steps_list):\n", "            for j, to_step in enumerate(all_steps_list):\n", "                matrix[i][j] = step_transitions[from_step][to_step]\n", "\n", "        # Create heatmap\n", "        fig = go.Figure(data=go.Heatmap(\n", "            z=matrix,\n", "            x=all_steps_list,\n", "            y=all_steps_list,\n", "            colorscale='Viridis',\n", "            text=matrix.astype(int),\n", "            texttemplate=\"%{text}\",\n", "            textfont={\"size\": 10},\n", "            hoverongaps=False\n", "        ))\n", "\n", "        fig.update_layout(\n", "            title=\"Combined Conversation Flow Heatmap (Actual Transitions)\",\n", "            xaxis_title=\"Next Step\",\n", "            yaxis_title=\"Current Step\",\n", "            height=600,\n", "            width=800,\n", "            xaxis={'side': 'bottom'},\n", "            yaxis={'autorange': 'reversed'}\n", "        )\n", "\n", "        return fig\n", "\n", "    def create_enhanced_policy_entity_chart(self):\n", "        \"\"\"\n", "        Create an accurate chart showing policy mentions detected by the enhanced NLP system\n", "        across all customers.\n", "        \"\"\"\n", "        all_policy_mentions_data = []\n", "        for customer_name, report_data in self.customer_reports.items():\n", "            collected_entities = report_data.get('call_report_data', {}).get('collected_entities', {})\n", "            policies_mentioned = collected_entities.get('policies_mentioned', [])\n", "            for policy in policies_mentioned:\n", "                all_policy_mentions_data.append({'Customer': customer_name, 'Policy': policy})\n", "\n", "        if not all_policy_mentions_data:\n", "            fig = go.Figure()\n", "            fig.add_annotation(\n", "                text=\"No policy mentions detected in any conversation.\",\n", "                xref=\"paper\", yref=\"paper\",\n", "                x=0.5, y=0.5, xanchor='center', yanchor='middle',\n", "                showarrow=False, font=dict(size=14)\n", "            )\n", "            fig.update_layout(title=\"Combined Policy Mentions Across Conversations\", height=400)\n", "            return fig\n", "\n", "        policy_df = pd.DataFrame(all_policy_mentions_data)\n", "        # Count mentions per policy per customer\n", "        policy_counts = policy_df.groupby(['Customer', 'Policy']).size().reset_index(name='Count')\n", "\n", "        fig = px.bar(\n", "            policy_counts,\n", "            x='Policy',\n", "            y='Count',\n", "            color='Customer', # Use customer name as color/legend\n", "            title='Combined Policy Mentions Across Conversations',\n", "            color_discrete_sequence=self.colors,\n", "            text='Count',\n", "            barmode='group' # Group bars by policy, show customer bars next to each other\n", "        )\n", "        fig.update_traces(texttemplate='%{text}', textposition='outside')\n", "        fig.update_layout(xaxis_title=\"Policy Type\", yaxis_title=\"Number of Mentions\", height=600)\n", "        return fig\n", "\n", "    def create_sentiment_over_time_chart(self):\n", "        \"\"\"\n", "        Visualize sentiment polarity over the course of the conversation for all customers.\n", "        Each customer will have a separate line.\n", "        \"\"\"\n", "        all_sentiment_data = []\n", "        for customer_name, report_data in self.customer_reports.items():\n", "            df = report_data['processed_transcript_df'].copy()\n", "            if not df.empty:\n", "                df['Customer'] = customer_name\n", "                all_sentiment_data.append(df)\n", "\n", "        if not all_sentiment_data:\n", "            fig = go.Figure()\n", "            fig.add_annotation(\n", "                text=\"No sentiment data available for any conversation.\",\n", "                xref=\"paper\", yref=\"paper\",\n", "                x=0.5, y=0.5, xanchor='center', yanchor='middle',\n", "                showarrow=False, font=dict(size=14)\n", "            )\n", "            fig.update_layout(title=\"Combined Sentiment Over Time\", height=400)\n", "            return fig\n", "\n", "        combined_df = pd.concat(all_sentiment_data)\n", "\n", "        fig = px.line(\n", "            combined_df,\n", "            x='turn_index',\n", "            y='sentiment_compound',\n", "            color='Customer', # Separate lines for each customer\n", "            line_dash='speaker', # Different dash styles for Human/<PERSON><PERSON>\n", "            title='Combined Sentiment Polarity Over Time',\n", "            labels={'turn_index': 'Conversation Turn', 'sentiment_compound': 'Sentiment Polarity (Compound)'},\n", "            hover_data={'utterance': True, 'sentiment_emotion': True, 'speaker': True},\n", "            color_discrete_sequence=px.colors.qualitative.Dark24 # Use a larger color palette for multiple customers/speakers\n", "        )\n", "        fig.update_layout(yaxis_range=[-1, 1], height=600)\n", "        fig.add_hline(y=0.05, line_dash=\"dot\", line_color=\"green\", annotation_text=\"Positive Threshold\")\n", "        fig.add_hline(y=-0.05, line_dash=\"dot\", line_color=\"red\", annotation_text=\"Negative Threshold\")\n", "        return fig\n", "\n", "    def create_language_distribution_chart(self):\n", "        \"\"\"\n", "        Visualize the distribution of languages used by Human and Chat Bot, combined for all customers.\n", "        \"\"\"\n", "        all_lang_data = []\n", "        for customer_name, report_data in self.customer_reports.items():\n", "            df = report_data['processed_transcript_df'].copy()\n", "            if not df.empty:\n", "                df['Customer'] = customer_name\n", "                all_lang_data.append(df)\n", "\n", "        if not all_lang_data:\n", "            fig = go.Figure()\n", "            fig.add_annotation(\n", "                text=\"No language data available for any conversation.\",\n", "                xref=\"paper\", yref=\"paper\",\n", "                x=0.5, y=0.5, xanchor='center', yanchor='middle',\n", "                showarrow=False, font=dict(size=14)\n", "            )\n", "            fig.update_layout(title=\"Combined Language Distribution\", height=400)\n", "            return fig\n", "\n", "        combined_df = pd.concat(all_lang_data)\n", "        lang_counts = combined_df.groupby(['Customer', 'speaker', 'language']).size().reset_index(name='count')\n", "\n", "        fig = px.bar(\n", "            lang_counts,\n", "            x='speaker',\n", "            y='count',\n", "            color='language',\n", "            facet_col='Customer', # Create separate columns for each customer\n", "            title='Combined Language Distribution by Speaker and Customer',\n", "            labels={'count': 'Number of Utterances', 'speaker': 'Speaker', 'language': 'Language'},\n", "            color_discrete_sequence=self.colors,\n", "            text='count',\n", "            barmode='stack' # Stack languages within each speaker/customer group\n", "        )\n", "        fig.update_traces(texttemplate='%{text}', textposition='outside')\n", "        fig.update_layout(height=600)\n", "        return fig\n", "\n", "    def create_entity_extraction_overview(self):\n", "        \"\"\"\n", "        Visualize an overview of extracted entities across all customers.\n", "        \"\"\"\n", "        all_entities_data = []\n", "        for customer_name, report_data in self.customer_reports.items():\n", "            df = report_data['processed_transcript_df'].copy()\n", "            if not df.empty:\n", "                for _, row in df.iterrows():\n", "                    entities = row['extracted_entities']\n", "                    for ent_type, ent_val in entities.items():\n", "                        if ent_val and ent_type not in ['confirmations', 'intent_indicators']:\n", "                            if isinstance(ent_val, list):\n", "                                for item in ent_val:\n", "                                    all_entities_data.append({'Customer': customer_name, 'Entity Type': ent_type, 'Entity Value': item})\n", "                            else:\n", "                                all_entities_data.append({'Customer': customer_name, 'Entity Type': ent_type, 'Entity Value': ent_val})\n", "\n", "        if not all_entities_data:\n", "            fig = go.Figure()\n", "            fig.add_annotation(\n", "                text=\"No specific entities extracted across all conversations.\",\n", "                xref=\"paper\", yref=\"paper\",\n", "                x=0.5, y=0.5, xanchor='center', yanchor='middle',\n", "                showarrow=False, font=dict(size=14)\n", "            )\n", "            fig.update_layout(title=\"Combined Extracted Entities Overview\", height=400)\n", "            return fig\n", "\n", "        entity_df_raw = pd.DataFrame(all_entities_data)\n", "        entity_df_raw['Full_Entity_Label'] = entity_df_raw['Entity Type'] + ': ' + entity_df_raw['Entity Value'].astype(str)\n", "\n", "        # Count occurrences of each unique entity across all customers\n", "        entity_counts = entity_df_raw.groupby('Full_Entity_Label').size().reset_index(name='Count')\n", "        entity_counts = entity_counts.sort_values(by='Count', ascending=False).head(20) # Top 20 entities overall\n", "\n", "        # Now, for plotting, merge back with customer data to allow coloring by customer for the top entities\n", "        plot_df = pd.merge(entity_counts, entity_df_raw, on='Full_Entity_Label', how='left')\n", "        plot_df = plot_df.groupby(['Full_Entity_Label', 'Customer']).size().reset_index(name='Count_Per_Customer')\n", "\n", "        fig = px.bar(\n", "            plot_df,\n", "            x='Count_Per_Customer',\n", "            y='Full_Entity_Label',\n", "            color='Customer', # Color by customer\n", "            orientation='h',\n", "            title='Top Extracted Entities Overview (Combined)',\n", "            labels={'Count_Per_Customer': 'Frequency', 'Full_Entity_Label': 'Entity Type: Value'},\n", "            color_discrete_sequence=self.colors,\n", "            text='Count_Per_Customer',\n", "            barmode='stack' # Stack bars for each customer for a given entity\n", "        )\n", "        fig.update_traces(texttemplate='%{text}', textposition='outside')\n", "        fig.update_layout(yaxis={'categoryorder':'total ascending'}, height=700)\n", "        return fig\n", "\n", "    def create_step_completion_timeline(self):\n", "        \"\"\"\n", "        Visualize when each step was completed in the conversation for all customers.\n", "        Each customer will have a separate line of markers.\n", "        \"\"\"\n", "        all_timeline_data = []\n", "        for customer_name, report_data in self.customer_reports.items():\n", "            flow_data = report_data.get('call_report_data', {}).get('conversation_flow_data', {})\n", "            completion_timestamps = flow_data.get('completion_timestamps', {})\n", "\n", "            if completion_timestamps:\n", "                # Sort steps numerically for consistent display\n", "                sorted_steps = sorted(completion_timestamps.keys())\n", "                for step in sorted_steps:\n", "                    turn_index = completion_timestamps[step]\n", "                    all_timeline_data.append({'Customer': customer_name, 'Step': f\"Step {step}\", 'Turn Index': turn_index})\n", "\n", "        if not all_timeline_data:\n", "            fig = go.Figure()\n", "            fig.add_annotation(\n", "                text=\"No step completion data available for any conversation.\",\n", "                xref=\"paper\", yref=\"paper\",\n", "                x=0.5, y=0.5, xanchor='center', yanchor='middle',\n", "                showarrow=False, font=dict(size=14)\n", "            )\n", "            fig.update_layout(title=\"Combined Step Completion Timeline\", height=400)\n", "            return fig\n", "\n", "        timeline_df = pd.DataFrame(all_timeline_data).sort_values(by='Turn Index')\n", "\n", "        fig = px.scatter(\n", "            timeline_df,\n", "            x='Turn Index',\n", "            y='Step',\n", "            text='Step',\n", "            color='Customer', # Separate colors for each customer\n", "            title='Combined Conversation Step Completion Timeline',\n", "            labels={'Turn Index': 'Conversation Turn Index', 'Step': 'Completed Step'},\n", "            color_discrete_sequence=self.colors\n", "        )\n", "        fig.update_traces(mode='markers+text', textposition='top center')\n", "        fig.update_layout(height=600)\n", "        return fig\n", "\n", "    def analyze_prompt_adherence_and_correctness(self) -> pd.DataFrame:\n", "        \"\"\"\n", "        Performs an analysis across all transcripts to check prompt adherence\n", "        and the correctness of bot's policy suggestions/outcomes.\n", "        \"\"\"\n", "        analysis_results = []\n", "\n", "        for customer_name, report_data in self.customer_reports.items():\n", "            call_report = report_data['call_report_data']\n", "            collected_entities = call_report['collected_entities']\n", "            conversation_flow_data = call_report['conversation_flow_data']\n", "\n", "            # --- 1. Flow Adherence ---\n", "            flow_adherence = \"Adherent\"\n", "            completed_steps = sorted(conversation_flow_data['completed_steps'])\n", "\n", "            # Check for non-sequential jumps or excessive backtracking.\n", "            if len(completed_steps) > 1:\n", "                for i in range(len(completed_steps) - 1):\n", "                    # Define steps where jumps are expected/allowed (e.g., fallback steps, end of call)\n", "                    allowed_jump_targets = [41, 22] # End call, Schedule Callback\n", "                    allowed_jump_from_steps = [2, 10, 11, 12, 13, 14, 25, 31, 32, 33, 34, 36, 37, 38, 39, 40]\n", "\n", "                    if completed_steps[i+1] < completed_steps[i]: # Backtracking/Loop\n", "                        flow_adherence = \"Partial Adherence (Backtracking/Loop)\"\n", "                        break\n", "                    elif completed_steps[i+1] > completed_steps[i] + 1: # Skipped steps (non-sequential forward)\n", "                        if completed_steps[i+1] not in allowed_jump_targets and \\\n", "                           completed_steps[i] not in allowed_jump_from_steps:\n", "                            flow_adherence = \"Partial Adherence (Skipped Steps)\"\n", "                            break\n", "                        # Additional check for large, unexpected skips\n", "                        if (completed_steps[i+1] - completed_steps[i]) > 5: # Arbitrary large jump\n", "                             flow_adherence = \"Partial Adherence (Large Skip)\"\n", "                             break\n", "\n", "\n", "            # Check for repetition rule adherence\n", "            bot_utterances_texts = [p['utterance'] for p in conversation_flow_data['bot_patterns']]\n", "            if self._detect_repetition(bot_utterances_texts):\n", "                if \"Adherent\" in flow_adherence: # If not already marked by a worse adherence issue\n", "                    flow_adherence = \"Partial Adherence (Repetition)\"\n", "\n", "            # --- 2. Policy Suggestion Correctness ---\n", "            expected_policy = self._determine_expected_policy(collected_entities)\n", "            bot_suggested_policy = call_report.get('bot_suggested_policy')\n", "\n", "            policy_correctness = \"Not Applicable\" # Default if no policy was expected or suggested\n", "            if expected_policy:\n", "                if bot_suggested_policy == expected_policy:\n", "                    policy_correctness = \"Correct\"\n", "                else:\n", "                    policy_correctness = f\"Incorrect (Expected: {expected_policy}, Bot: {bot_suggested_policy if bot_suggested_policy else 'None'})\"\n", "            elif bot_suggested_policy: # <PERSON><PERSON> suggested something when no clear policy was expected based on rules\n", "                 policy_correctness = f\"Unexpected Suggestion (Bot: {bot_suggested_policy})\"\n", "\n", "            # --- 3. Expected Outcome Achieved ---\n", "            outcome_achieved = \"Not Achieved\" # Default to not achieved\n", "            final_step = conversation_flow_data['current_step']\n", "\n", "            # Check for successful completion based on different possible end states in your prompt\n", "            if final_step == 41: # Goodbye/Call End\n", "                outcome_achieved = \"Call Ended Gracefully\"\n", "\n", "            if 22 in completed_steps: # Callback scheduled\n", "                 outcome_achieved = \"Callback Scheduled\"\n", "\n", "            # If a correct policy was suggested AND the conversation ended gracefully or moved towards application/quotation\n", "            if policy_correctness == \"Correct\" and final_step in [16, 17, 18, 19, 20, 21, 41]: # Moved to post-suggestion steps\n", "                outcome_achieved = \"Policy Suggested & Advanced\"\n", "            elif policy_correctness == \"Correct\": # Policy correctly suggested but conversation ended without further progress\n", "                outcome_achieved = \"Policy Suggested (Call Ended Prematurely)\"\n", "\n", "            # If no policy was suggested but call ended gracefully (e.g., human disinterest)\n", "            elif not bot_suggested_policy and final_step == 41 and 25 in completed_steps: # Ended due to disinterest\n", "                outcome_achieved = \"Call Ended Gracefully (Customer Disinterest)\"\n", "            elif not bot_suggested_policy and final_step == 41:\n", "                outcome_achieved = \"Call Ended Gracefully (No Policy Suggested)\"\n", "\n", "\n", "            analysis_results.append({\n", "                'Customer Name': customer_name,\n", "                'Total Turns': call_report['total_turns'],\n", "                'Flow Adherence': flow_adherence,\n", "                'Policy Suggestion Correctness': policy_correctness,\n", "                'Outcome Achieved': outcome_achieved,\n", "                'Final Step': final_step,\n", "                'Bot Suggested Policy': bot_suggested_policy,\n", "                'Expected Policy (Inferred)': expected_policy,\n", "                # 'Collected Entities': collected_entities # Can be re-enabled for detailed debugging\n", "            })\n", "\n", "        return pd.DataFrame(analysis_results)\n", "\n", "    def _determine_expected_policy(self, entities: Dict) -> Optional[str]:\n", "        \"\"\"\n", "        Infers the expected policy based on collected entities and prompt rules (Step 15 conditions).\n", "        \"\"\"\n", "        age = entities.get('age')\n", "        age_months = entities.get('age_months') # For cases like \"5 months old\"\n", "\n", "        # If age is 0 and age_months is present, it means very young, effectively 0 years old\n", "        if age == 0 and age_months is not None:\n", "            pass # Keep age as 0\n", "\n", "        medical_conditions = entities.get('medical_conditions', [])\n", "        # Check for any pre-existing disease (PED)\n", "        # Check against lowercased values for robustness\n", "        has_ped = len(medical_conditions) > 0 or \\\n", "                  (entities.get('medications_status') and entities['medications_status'].lower() not in ['no', 'none', 'नहीं', 'नही']) or \\\n", "                  (entities.get('hospitalization_history') and entities['hospitalization_history'].lower() not in ['no', 'none', 'never']) or \\\n", "                  (entities.get('doctor_recommendation_status') and entities['doctor_recommendation_status'].lower() not in ['no', 'none'])\n", "\n", "        # Policy Flow Conditions from prompt (Step 15)\n", "        # • Strictly If the user or any family member has a Pre-Existing Disease (PED), then go to Step 15.1 and recommend the FIRST policy: Activ One Vital .\n", "        if has_ped:\n", "            return \"Activ One Vital\"\n", "\n", "        # If no PED:\n", "        # • Strictly If no one has PED and any one family member's age is ≥ 55, then go to Step 15.4 and recommend the FIRST policy: Activ One Maxplus.\n", "        # • Strictly If no one has PED and any one family member's age is between 36 and 54 (inclusive of 36, exclusive of 55), then go to Step 15.3 and recommend the FIRST policy: Activ One Maxplus.\n", "        # • Strictly If no one has PED and all family members are aged ≤ 35, then go to Step 15.2 and recommend the FIRST policy: Activ Fit.\n", "\n", "        effective_age = age\n", "        # Assuming `family_members_ages` might be a list of ages if extracted\n", "        if 'family_members_ages' in entities and isinstance(entities['family_members_ages'], list):\n", "            all_ages = [a for a in entities['family_members_ages'] if a is not None]\n", "            if age is not None: # Include primary applicant's age if available\n", "                all_ages.append(age)\n", "            if all_ages:\n", "                effective_age = max(all_ages)\n", "\n", "        # Special handling for <PERSON><PERSON><PERSON>'s child's age (5 months, which results in age 0)\n", "        # If effective_age is 0, it should fall into <=35 category.\n", "\n", "        if effective_age is not None:\n", "            if effective_age >= 55:\n", "                return \"Activ One Maxplus\"\n", "            elif 36 <= effective_age <= 54:\n", "                return \"Activ One Maxplus\"\n", "            elif effective_age <= 35: # This includes age 0 (5 months old)\n", "                return \"Activ Fit\"\n", "\n", "        # • Strictly if the customer has not shared any details about pre-existing diseases or\n", "        # medications, hospitalization history and also personal details like age or family\n", "        # member details, but they still want to know about the policy then suggest them \"Activ One N-X-T\".\n", "        # This condition is typically a fallback when critical information is missing.\n", "        # If no PED and no effective age was collected, then suggest Activ One N-X-T.\n", "        if not has_ped and effective_age is None:\n", "             return \"Activ One N-X-T\"\n", "\n", "        return None # No specific policy can be strictly inferred from prompt conditions\n", "\n", "    def _detect_repetition(self, utterances: List[str], window_size: int = 2, similarity_threshold: float = 0.95) -> bool:\n", "        \"\"\"\n", "        Detects if the bot repeatedly asks the same question or states the same intent.\n", "        Looks for highly similar consecutive utterances.\n", "        \"\"\"\n", "        if len(utterances) < 2:\n", "            return False\n", "\n", "        # Filter out very short or non-informative utterances before checking similarity\n", "        filtered_utterances = [u for u in utterances if len(u.split()) > 3] # Only consider utterances with more than 3 words\n", "\n", "        if len(filtered_utterances) < 2:\n", "            return False\n", "\n", "        vectorizer = TfidfVectorizer(max_features=1000)\n", "\n", "        # Fit on all filtered utterances to get a consistent vocabulary\n", "        try:\n", "            vectorizer.fit(filtered_utterances)\n", "        except ValueError: # Handle case where all filtered utterances are too similar or empty\n", "            return False\n", "\n", "        for i in range(len(filtered_utterances) - 1):\n", "            u1_text = filtered_utterances[i].lower()\n", "            u2_text = filtered_utterances[i+1].lower()\n", "\n", "            if not u1_text or not u2_text:\n", "                continue\n", "\n", "            u1_vector = vectorizer.transform([u1_text])\n", "            u2_vector = vectorizer.transform([u2_text])\n", "\n", "            # Check if vectors are non-zero before calculating similarity\n", "            if u1_vector.nnz > 0 and u2_vector.nnz > 0:\n", "                if cosine_similarity(u1_vector, u2_vector)[0, 0] >= similarity_threshold:\n", "                    return True\n", "        return False\n", "\n", "\n", "def extract_customer_name(transcript_text: str) -> str:\n", "    \"\"\"Extracts the customer name from the first line of the transcript.\"\"\"\n", "    first_line = transcript_text.strip().split('\\n')[0]\n", "    # Pattern for \"क्या मैं <name> से बात कर रही हूँ?\"\n", "    match = re.search(r\"क्या मैं ([\\w\\s]+) से बात कर रही हूँ\\?\", first_line)\n", "    if match:\n", "        return match.group(1).strip()\n", "\n", "    # Pattern for \"क्या मैं <name> से बात कर रहा हूँ?\" (male speaker)\n", "    match_alt = re.search(r\"क्या मैं ([\\w\\s]+) से बात कर रहा हूँ\\?\", first_line)\n", "    if match_alt:\n", "        return match_alt.group(1).strip()\n", "\n", "    return \"Unknown Customer\" # Default if name not found\n", "\n", "\n", "# --- Main Execution ---\n", "\n", "if __name__ == \"__main__\":\n", "    # Define multiple raw transcripts\n", "    transcripts_raw = [\n", "        # Transcript 1 (<PERSON><PERSON><PERSON>)\n", "        \"\"\"\n", "Chat Bot: नमस्ते,मैं <PERSON><PERSON><PERSON> बोल रही हूँ aditya birla health insurance से, क्या मैं mukesh से बात कर रही हूँ?\n", "Human: Hello hello\n", "Human: Hello.\n", "<PERSON><PERSON>: Hello, I am <PERSON><PERSON><PERSON> from Aditya Birla Health Insurance.\n", "Cha<PERSON>: Please note this call is being recorded for quality and monitoring purposes.\n", "<PERSON><PERSON>: Hope that's fine?\n", "Human: बोलिए बोलिए.\n", "Chat Bot: हमें आपकी request Aditya Birla Health Insurance website से मिली है.\n", "Chat Bot: तो मैं बस यह check करना चाहती थी कि क्या आप fresh policy की तलाश में हैं, या अपनी existing policy को port करने के बारे में सोच रहे हैं?\n", "Human: Quality की class में है\n", "Chat Bot: ठीक है, तो आप एक नई health insurance policy लेना चाहते हैं, correct?\n", "Human: Yes.\n", "<PERSON><PERSON>: Great, and to proceed further, may I know your age?\n", "Human: Twenty twenty four\n", "<PERSON><PERSON>: Thank you for sharing that.\n", "<PERSON><PERSON>: Just to confirm, is Four Eight Four Six Six One the correct pincode for your city?\n", "Human: हां हां. Four four eight अड़तालीस चवालीस इकसठ है\n", "Chat Bot: अच्छा, क्या आप कोई regular physical activity करते हैं, जैसे walking, yoga, gym, या कुछ similar?\n", "Human: जी.\n", "Chat Bot: क्या आपकी कोई pre-existing health conditions हैं, जैसे asthma, diabetes, blood pressure, heart issues, या कुछ और जिसके बारे में हमें जानना चाहिए?\n", "Human: No no no कुछ.\n", "<PERSON><PERSON>: <silence>\n", "Chat Bot: क्या आप currently किसी health condition के\n", "Human: कुछ भी नहीं है.\n", "<PERSON><PERSON>: <silence>\n", "Chat Bot: अच्छा, क्या आप कभी अस्पताल में भर्ती हुए हैं?\n", "<PERSON><PERSON>: Hello?\n", "Human: No.\n", "Chat Bot: क्या किसी doctor ने recently किसी medical condition के लिए surgery या treatment recommend किया है?\n", "<PERSON><PERSON>: Hello?\n", "Human: नही<PERSON> नहीं नहीं.\n", "Human: नही<PERSON> नहीं.\n", "Chat Bot: ## Step 15: Policy Suggestion\n", "Chat Bot: समझ गई, धन्यवाद जानकारी के लिए.\n", "<PERSON><PERSON>: Based on the details you've shared, मैं \"Activ Fit\" policy की सलाह दूंगी.\n", "Chat <PERSON>t: यह policy specially young और healthy individuals के लिए designed है, जो कम premium में ज्यादा rewards चाहते हैं.\n", "Chat Bot: क्या आप इसके बारे में अधिक जानने में interested हैं?\n", "<PERSON><PERSON>: Hello?\n", "Human: Yes yes बोलिए.\n", "Human: हां हां बोलिए ना.\n", "<PERSON><PERSON>: If you choose the Activ Fit policy, the initial waiting period will be thirty\n", "<PERSON><PERSON>: <silence>\n", "Chat Bot: अगर आप \"Activ Fit\" policy लेते हैं, तो इसका initial waiting period thirty days का होगा, pre-existing diseases का waiting period three years का होगा, और specific diseases का waiting period two years का होगा.\n", "Chat Bot: क्या ये information आपकी help कर रही है?\n", "Human: हा<PERSON>.\n", "Chat Bot: अच्छा, मैं आपको Activ Fit के\n", "Human: Ok ok ठीक है.\n", "<PERSON><PERSON>: <silence>\n", "Chat Bot: हमारे discussion के basis पर, इस policy में features हैं जैसे Health Returns, Claim Protect, Super Reload, और Binge Refill.\n", "Chat Bot: क्या आप अब application के साथ आगे बढ़ना चाहेंगे?\n", "<PERSON><PERSON>: Hello?\n", "Human: हां बोलिए बोलिए.\n", "Chat <PERSON>t: Great, हमारे पास आपका email address है sa<PERSON><PERSON>sheight\n", "Human: हां बोलिए ना बोलिए ना.\n", "<PERSON><PERSON>: <silence>\n", "Chat Bot: हमारे पास आपका email address है sahumukesheight eight one seven at the rate gmail dot com, क्या यह correct है?\n", "Human: Ok ok.\n", "Chat Bot:\n", "Human: हां\n", "Chat Bot: जैसे ही आप insurance quotation जो हमने भेजा है उसे review कर लेंगे, मैं आपके convenience के अनुसार एक callback schedule कर दूंगी ताकि हम next steps discuss कर सकें.\n", "Chat Bot: आप एक convenient समय बताएँ ताकि मैं callback schedule कर सकूं?\n", "Human: Ok ok.\n", "Chat Bot: क्या यह सही रहेगा अगर मैं आपको कल दो बजे call करूं, या आप सुबह ग्यारह से शाम छह बजे के बीच कोई और time prefer करेंगे?\n", "Human: नहीं सुबह से करना sir सुबह से खाली रहेगा\n", "Human: Ok.\n", "<PERSON><PERSON>: Are you there?\n", "Chat Bot: कोई problem नहीं.\n", "Chat Bot: सुबह ग्यारह से शाम छह के बीच कौन सा समय आपके लिए सबसे convenient रहेगा?\n", "Human: Ya lo voy checar.\n", "Chat Bo<PERSON>: Lo siento, pero actualmente solo puedo asistir en inglés o hindi.\n", "Cha<PERSON>: ¿Le gustaría que organice una llamada de un asesor específico para su idioma?\n", "<PERSON><PERSON>: \"\n", "<PERSON><PERSON>: Hello?\n", "<PERSON><PERSON>: Hello?\n", "Human: हां हां बोलिए.\n", "Chat Bot: Great! मैं कल दोपहर <PERSON><PERSON><PERSON><PERSON> पर callback schedule कर रही हूँ. अगर आपके और कोई\n", "Human: हां हां बोलिए ना.\n", "<PERSON><PERSON>: <silence>\n", "Chat Bot: आपका time के लिए धन्यवाद.\n", "Chat Bot: मैं scheduled time पर follow up करूंगी.\n", "Chat Bot: अगर आपके पास और कोई questions हैं, तो हमें संपर्क करें.\n", "<PERSON><PERSON>: Have a great day. Goodbye.\n", "        \"\"\",\n", "        # Transcript 2 (<PERSON><PERSON><PERSON>)\n", "        \"\"\"\n", "Chat Bot: नमस्ते, मैं <PERSON><PERSON> बोल रहा हूँ aditya birla health insurance से, क्या मैं <PERSON><PERSON>am से बात कर रहा हूँ?\n", "Human: Yes. This is <PERSON>.\n", "<PERSON><PERSON>: Thank you, <PERSON>.\n", "Chat Bot: Please note इस call को quality और monitoring purpose के लिए record किया जा रहा है.\n", "<PERSON><PERSON>: Hope that's fine?\n", "Human: Yeah. That's fine.\n", "Chat Bot: Actually हमें आपकी request Aditya Birla Health Insurance website से मिली है.\n", "Chat Bot: तो मैं बस यह check करना चाहता था कि क्या आप fresh policy की तलाश में हैं, या अपनी existing policy को port करने के बारे में सोच रहे हैं?\n", "Human: No. I don't have a policy as of now.\n", "Chat Bot: अच्छा, तो आप नई health insurance policy लेना चाहते हैं। आपको better guide करने के लिए - क्या आप सिर्फ अपने लिए coverage चाहते हैं, या family members को भी include करना चाहते हैं?\n", "Human: No. So I wanted to understand, like, doesn't this really work after I grow to a certain age like today I have no health condition but later on I may need some cover. So I just wanted to understand why I need health insurance.\n", "Cha<PERSON> Bot: Health insurance आपके लिए एक financial safety net की तरह काम करता है, जो आपको unexpected medical expenses से protect करता है.\n", "Chat Bot: आज भले ही आपको कोई health condition न हो, लेकिन future में health risks बढ़ सकते हैं जैसे age increase होने के साथ, और कभी-कभी अचानक hospitalization या medical treatment की जरूरत भी पड़ सकती है, जो काफी महंगी हो सकती है.\n", "Chat Bot: Health insurance की वजह से न सिर्फ आप financial burdens से बचे रह सकते हैं, बल्कि आप time पर और बिना चिंता किए आवश्यक medical care भी ले सकते हैं.\n", "Chat Bot: इसके साथ ही, कुछ policies health management benefits भी offer करती हैं जैसे annual health checkups, जिसके तहत आप अपनी health का regular checkup maintain कर सकते हैं.\n", "Chat Bot: बीमारी या medical emergencies कभी भी आ सकती हैं.\n", "Cha<PERSON> Bot: Health insurance आपके लिए एक financial safety net की तरह काम करता है, जो आपको\n", "Human: So how much will it so how much will you charge me with?\n", "Chat Bot: Great, आगे बढ़ने के लिए आपकी कुछ details चाहिए होंगी जिससे कि हम आपकी specific needs के अनुसार सही policy recommend कर सकें। क्या मैं आपकी age जान सकता हूँ?\n", "Human: No. I shared my age, but, like, what if I change job or I'm changing locations? Would my policy still old?\n", "<PERSON><PERSON>: Yes, absolutely. Once you have a health insurance policy, it remains valid even if you change jobs or relocate. Your policy is with you regardless of your employment status or location. You just need to make sure to inform us about your new address or any other contact details so that your records stay updated for any communication. Do you have any other questions or would you like to proceed with exploring policy\n", "Human: So let's say I make the payment today, you will not ask me for money again next year or the year after that, right? Like once I make a health insurance payment, it there. Like like like a life insurance.\n", "<PERSON><PERSON>: <silence>\n", "Chat Bot: Health insurance का purpose different होता है.\n", "Chat Bot: Health insurance एक annual contract की तरह होता है, जिसका मतलब है कि आपको हर साल policy renew करनी होगी और इसके लिए annual premium pay करना होगा.\n", "<PERSON><PERSON>: Health insurance life insurance की तरह one-time payment या whole life coverage की तरह नहीं होता है.\n", "Chat Bot: Annual renewal की process से policyholder की needs, premium, और coverages को साल दर साल adjust किया जा सकता है.\n", "Chat Bot: इस तरीके से, आप अपनी policy में बदलाव भी कर सकते हैं, जैसे coverage amount adjust करना या add-on benefits लेना.\n", "Chat Bot: अगर आप ज्यादा details में जानना चाहते हैं या एक specific quote देखना चाहते हैं, तो मैं आपकी age और कुछ basic information collect करके आपको best options बता सकता हूं.\n", "Human: No. So, like, can I sort of make a payment today? But, you know, because I don't have a condition, can I pause it and then restart it, let's say, one or two years later?\n", "<PERSON><PERSON>: Health insurance policies generally can't be paused and restarted like that.\n", "Chat Bot: They need to be maintained continuously through regular premium payments.\n", "<PERSON><PERSON>: If you skip a renewal, the policy lapses, and you lose all accumulated benefits, like waiting period credit and coverage continuity.\n", "<PERSON><PERSON>: If you choose to get a policy again later, you'd need to reapply and go through the waiting periods anew.\n", "<PERSON><PERSON>: It's often more beneficial to keep your policy active consistently to maintain continuous coverage, especially since health issues can arise unexpectedly.\n", "<PERSON><PERSON>: Do you have any other questions about health insurance policies or coverage?\n", "Human: So can I add my partner, like, after I get married?\n", "<PERSON><PERSON>: Yes, absolutely! After you get married, you can definitely add your partner to your existing health insurance policy.\n", "Chat <PERSON>: This typically involves notifying your insurance provider about the change in your family status and submitting any required documents related to your marriage.\n", "<PERSON><PERSON>: It's a good way to ensure that both you and your partner have comprehensive health coverage under one plan.\n", "<PERSON><PERSON>: If you need help with this process or have any more questions, feel free to ask!\n", "Human: So, like, today, have bought a plan and, you know, there's some waiting period that I've already, you know, sort of waited for. And then I get married. So do that applies to my partner as well. Like, she will not have to then go through the waiting period again.\n", "<PERSON><PERSON>: When you add your partner to your existing health insurance policy, she would typically have her own waiting periods that would need to be completed, even if you have already completed yours.\n", "Chat <PERSON>: The waiting periods apply individually to each policy member.\n", "<PERSON><PERSON>: However, some insurance providers might offer certain terms or discounts based on your existing coverage, so it's always good to check the specific terms with your insurance provider.\n", "<PERSON><PERSON>: If you have any questions or need further assistance on this, feel free to ask.\n", "Human: So who is this insurance provider who can give me this?\n", "Chat Bot: मैं सिर्फ Aditya Birla Health Insurance का Al representative हूं.\n", "Chat Bot: क्या आपको Health Insurance से जुड़ा कोई doubt है?\n", "Human: So which insurance provider will allow give me this discount?\n", "Human: <PERSON><PERSON> दे देगा यह?\n", "Chat Bot: मैं सिर्फ Aditya Birla Health Insurance का representative हूं.\n", "Chat Bot: क्या आपको Aditya Birla Health Insurance के बारे में कोई doubt है?\n", "Human: Link with our Thank you.\n", "Human: So much.\n", "Human: Bye.\n", "<PERSON><PERSON>: Goodbye.\n", "        \"\"\",\n", "        # Transcript 3 (<PERSON>)\n", "        \"\"\"\n", "Chat Bot: नमस्ते, मैं <PERSON><PERSON> बोल रहा हूँ aditya birla health insurance से, क्या मैं Ibrahim से बात कर रहा हूँ?\n", "Human: हां जी बोलिए.\n", "Chat Bot: धन्यवाद. कृपया ध्यान दें कि यह कॉल quality और monitoring purpose के लिए record की जा रही है.\n", "Chat Bot: आशा करता हूँ कि आपको यह ठीक लगेगा?\n", "<PERSON><PERSON>: Are you there?\n", "Human: Person you are speaking\n", "Chat Bot: Actually हमें आपकी request Aditya Birla Health Insurance website से मिली है.\n", "Chat Bot: तो मैं यह check करना चाहता था कि क्या आप fresh policy की तलाश में हैं, या अपनी existing policy को port करने के बारे में सोच रहे हैं?\n", "Human: fresh policy के लिए.\n", "Chat Bot: अच्छा, तो आप नई health insurance policy लेना चाहते हैं.\n", "Chat Bot: आ<PERSON><PERSON><PERSON> better guide करने के लिए - क्या आप सिर्फ अपने लिए coverage चाहते हैं, या family members को भी include करना चाहते हैं?\n", "Human: Family members will be\n", "Chat Bot: Perfect, बस एक बात confirm कर दीजिए - आप कितने family members को policy में include करना चाहते हैं?\n", "Human: सिर्फ एक, me and my wife.\n", "Chat Bot: आपकी और आपकी wife की age क्या है?\n", "Human: <PERSON> sa<PERSON>.\n", "Chat Bot: धन्यवाद! मेरे record के अनुसार आपका pincode six zero zero zero one four है, क्या यह सही है?\n", "Human: हा<PERSON>.\n", "Chat Bot: धन्यवाद, आगे बढ़ते हैं। क्या आप या आपकी wife कोई regular physical activity करते हैं जैसे walking, yoga, gym?\n", "Human: नहीं नहीं नहीं generally sir walking.\n", "Chat Bot:\n", "Human: Walking and सिर्फ generally walking करते हैं हम.\n", "Human: हां diabetes diabetes है.\n", "Chat Bot: समझ गया, यह जानकर अच्छा लगा! क्या आपकी या आपकी wife की कोई pre-existing health conditions हैं जैसे asthma, diabetes, blood pressure, heart issues, या\n", "<PERSON><PERSON>: <silence>\n", "Chat Bot: मुझे यह सुनकर खेद है कि आपकी wife को diabetes है.\n", "Chat Bot: हम आपकी सहायता के लिए उचित नीति खोजने का प्रयास करेंगे जो आपके स्वास्थ्य की ज़रूरतों को पूरा करे.\n", "Chat Bot: क्या आप currently किसी health condition के लिए कोई medications ले रहे हैं?\n", "Human: Diabetes or cholesterol.\n", "Chat Bot: समझ गया, आपकी स्थिति के लिए हम बहुत समझ रखते हैं। क्या आप या आपकी wife recently hospitalized हुए हैं या क्या किसी doctor नै recently किसी medical condition के लिए surgery या treatment recommend किया है?\n", "Human: नहीं नहीं, January में मैं hospitalized हुआ था.\n", "Human: <PERSON> के लिए.\n", "Human: Removal of kidney stone के लिए.\n", "Chat Bot: मुझे खेद है कि आपको यह अनुभव करना पड़ा। हम आपकी स्थिति को बेहतर ढंग से समर्थन करने के लिए यहाँ हैं। आपकी जानकारी के आधार पर, मैं आपको Activ One V-Y-T-L policy की सिफारिश\n", "कर सकता हूँ। यह policy Day one से ही chronic diseases, जैसे diabetes, cover करती है और\n", "chronic management program भी प्रदान करती है। क्या आप इसके बारे में और जानना चाहेंगे?\n", "Human: हम बताइए.\n", "Chat Bot: Activ One VYTL policy पूरी तरह से peace of mind के लिए विशेष रूप से design की गई है,\n", "खासकर उन लोगों के लिए जिनको chronic problems हैं या होने का chance है.\n", "Chat Bot: इसमें Day one से ही chronic diseases जैसे कि diabetes, BP, asthma, और अन्य\n", "conditions बिना किसी waiting period के cover होती है.\n", "Chat Bot: इसमें chronic management program भी शामिल है जिसमें personal health coach,\n", "specialist consultation, और जरूरी tests शामिल हैं आपकी chronic illness को manage करने के लिए.\n", "Chat Bot: इसके अलावा, consumables cover किए जाते हैं, और आप up to hundred percent health\n", "returns भी earn कर सकते हैं जो अगले साल के renewal premium को कम करता है.\n", "Chat Bot: क्या आप इसके साथ आगे बढ़ना चाहेंगे या इसमें कोई और जानकारी चाहिए?\n", "<PERSON><PERSON>: Hello?\n", "Human: बताइए बताइए.\n", "<PERSON><PERSON> Bot: Activ One VYTL policy के मुख्य फायदे हैं:\n", "Chat Bot: - Day One से ही chronic diseases cover करती है जैसे कि आपकी diabetes और अन्य\n", "conditions.\n", "Chat Bot: - इसमें chronic management program मिलता है जिसमें personal health coach और\n", "specialist consultations शामिल हैं.\n", "Chat Bot: - Consumables जैसे gloves और PPE covered होते हैं.\n", "Chat Bot: - Up to hundred percent health returns जो अगले साल के renewal premium को कम\n", "करते हैं.\n", "Chat Bot: इस policy के साथ proceed करने के लिए क्या मैं आपके registered email address पर\n", "quotation भेज सकता हूँ?\n", "<PERSON><PERSON>: Hello?\n", "Human: हां, quotation भेजी है.\n", "Human: Existing हां.\n", "Chat Bot: Great! हमारे पास आपका email address\n", "Human: PED के लिए print\n", "<PERSON><PERSON>: <silence>\n", "Human: waiting period क्या है?\n", "Chat Bot: Activ One VYTL policy के तहत pre-existing diseases के लिए waiting period तीन साल\n", "है। लेकिन खास बात यह है कि chronic diseases जैसे diabetes Day One से ही cover होती हैं। क्या मैं\n", "आपके लिए कुछ और assist कर सकता हूँ?\n", "Human: उसके लिए reduction नहीं हो सकता.\n", "Human: तीन साल से.\n", "Human: कम\n", "Chat Bot: खेद है, लेकिन Activ One VYTL policy में pre-existing diseases के लिए जो standard\n", "waiting period है, उसे reduce करने का कोई option नहीं है। यह standard policy term का हिस्सा है।\n", "आपका कोई और सवाल है तो बताएं, मैं आपकी मदद करने के लिए यहाँ हूँ।\n", "<PERSON><PERSON>: Hello?\n", "Human: Hello?\n", "Chat Bot: नमस्ते, मैं <PERSON><PERSON> बोल रहा हूँ aditya birla health insurance से, क्या मैं <PERSON><PERSON>n से बात\n", "कर रहा हूँ?\n", "Human: हां हां बोलिए ना.\n", "        \"\"\",\n", "        # Transcript 4 (<PERSON><PERSON><PERSON>)\n", "        \"\"\"\n", "Chat Bot: नमस्ते, मैं <PERSON><PERSON> बोल रहा हूँ aditya birla health insurance से, क्या मैं Chetan Thakur से बात कर रहा हूँ?\n", "Human: <PERSON><PERSON><PERSON> जी.\n", "Chat Bot:\n", "Human: <PERSON><PERSON><PERSON> जी.\n", "<PERSON><PERSON>: Great,Thank you. Please note यह Call quality और monitoring purpose के लिए\n", "record की जा रही है.\n", "<PERSON><PERSON>: Hope that's fine?\n", "Human: बताइए हां.\n", "Chat Bot: Actually हमें आपकी request Aditya Birla Health Insurance website से मिली है.\n", "Chat Bot: तो मैं बस यह check करना चाहता था कि क्या आप fresh policy की तलाश में हैं, या अपनी\n", "existing policy को port करने के बारे में सोच रहे हैं?\n", "Human: Main pastilla.\n", "Human: पैसे देख रहा था.\n", "Chat Bot: अच्छा, तो आप नई health insurance policy लेना चाहते हैं, आपको better guide करने के\n", "लिए - क्या आप सिर्फ अपने लिए coverage चाहते हैं, या family members को भी include करना चाहते\n", "हैं?\n", "Human: Family है.\n", "Chat Bot: Perfect, बस एक बात confirm कर दीजिए - आप कितने family members को policy में\n", "include करना चाहते हैं?\n", "Human: मैं हूं मेरी wife और एक बच्चा है.\n", "Chat <PERSON>t: Great, क्या आप अपने सभी family members की ages बता सकते हैं?\n", "Human: मेरे उनतीस साल है, wife की छब्बीस है.\n", "Human: और बच्चा भी पांच महीने का है.\n", "Chat Bot: धन्यवाद, Confirm करने के लिए, क्या two zero one three zero one आप के city का सही\n", "pincode है?\n", "Human: Two zero हां यही है.\n", "Chat Bot: Perfect! Main अब next step पर proceed करता हूं.\n", "Chat Bot: अच्छा, क्या आप या आपके family members कोई regular exercise या wellness routine\n", "follow करते हैं - जैसे yoga, walking, या कुछ और?\n", "Human: हमारा जैसे घर का ही काम है जो खेती बाड़ी है तो इस ही नहीं पड़ती.\n", "Chat Bot: समझ गया. क्या आपकी या आपके family members की कोई pre-existing health\n", "conditions हैं जैसे asthma, diabetes, BP, heart problems, या कुछ और\n", "Human: नहीं कुछ नहीं सब fit है. सब fit है. सब fit है.\n", "<PERSON><PERSON>: <silence>\n", "Chat Bot: अच्छा\n", "Human: कुछ भी नहीं अभी तो फ़िलहाल सब fit है.\n", "<PERSON><PERSON>: <silence>\n", "Chat Bot: Great, और आगे बढ़ने के लिए-\n", "Human: मैं कैसे बात कर रहा हूं normal बात कर रहा या लग रहा sir computer से वापस सुबह\n", "        \"\"\"\n", "    ]\n", "\n", "    # Define prompt_rules based on the provided document\n", "    prompt_rules = {\n", "        'steps': {\n", "            1: {\n", "                'name': 'Taking consent',\n", "                'bot_dialogues_keywords': ['call is being recorded', 'quality and monitoring purposes', 'fine'],\n", "                'expected_human_confirmations': ['yes', 'हां', 'हाँ', 'जी', 'ok', 'okay', 'ठीक', 'fine', 'yeah'],\n", "                'required_info': []\n", "            },\n", "            2: {\n", "                'name': 'Policy Type Inquiry',\n", "                'bot_dialogues_keywords': ['request', 'website', 'fresh policy', 'existing policy', 'port', 'नई policy', 'new', 'fresh policy', 'new one', 'कल ही ली है', 'अभी ली है', 'just yesterday', 'recently bought', 'नया policyholder हूँ', 'port', 'migrate', 'किसी और कंपनी से port', 'existing policy दूसरी कंपनी की', 'मेरे पास ABHI की policy है', '<PERSON><PERSON><PERSON> का ग्राहक हूँ', 'existing policy है', 'अपनी policy में बदलाव', 'add members', 'check status'],\n", "                'expected_human_confirmations': ['yes', 'no', 'ठीक', 'correct', 'हां', 'हाँ'],\n", "                'required_info': ['policy_type_interest'],\n", "                'human_intents': {\n", "                    'new_policy': ['नई policy', 'new', 'fresh policy', 'new one'],\n", "                    'recent_purchase': ['कल ही ली है', 'अभी ली है', 'just yesterday', 'recently bought', 'नया policyholder हूँ'],\n", "                    'porting': ['port', 'migrate', 'किसी और कंपनी से port', 'existing policy दूसरी कंपनी की'],\n", "                    'existing_abhi': ['मेरे पास ABHI की policy है', '<PERSON><PERSON><PERSON> का ग्राहक हूँ', 'existing policy है', 'अपनी policy में बदलाव', 'add members', 'check status']\n", "                }\n", "            },\n", "            3: {\n", "                'name': 'Acknowledge New Policy Interest',\n", "                'bot_dialogues_keywords': ['नई health insurance policy', 'guide', 'coverage', 'family members', 'अपने लिए coverage', 'family members को भी include'],\n", "                'expected_human_confirmations': ['yes', 'no', 'ठीक', 'correct', 'हां', 'हाँ'],\n", "                'required_info': ['coverage_scope']\n", "            },\n", "            4: {\n", "                'name': 'Acknowledge Portability Interest',\n", "                'bot_dialogues_keywords': ['current health insurance policy को port', 'help कर सकते हैं', 'current insurer का नाम', 'policy details share'],\n", "                'expected_human_confirmations': ['yes', 'no', 'ठीक', 'correct', 'हां', 'हाँ'],\n", "                'required_info': ['insurer_details', 'policy_details_for_port']\n", "            },\n", "            5: {\n", "                'name': 'Family Coverage Details',\n", "                'bot_dialogues_keywords': ['family members', 'include'],\n", "                'expected_human_confirmations': ['yes', 'no', 'ठीक', 'correct', 'हां', 'हाँ'],\n", "                'required_info': ['num_family_members']\n", "            },\n", "            6: {\n", "                'name': 'Collect Family Ages',\n", "                'bot_dialogues_keywords': ['सभी की ages collect', 'customer\\'s age', 'family members ages'],\n", "                'expected_human_confirmations': ['yes', 'no', 'ठीक', 'correct', 'हां', 'हाँ'],\n", "                'required_info': ['age']\n", "            },\n", "            7: {\n", "                'name': 'Collect Self Age',\n", "                'bot_dialogues_keywords': ['age जान सकता हूँ', 'आगे बढ़ने के लिए'],\n", "                'expected_human_confirmations': ['yes', 'no', 'ठीक', 'correct', 'हां', 'हाँ'],\n", "                'required_info': ['age']\n", "            },\n", "            8: {\n", "                'name': '<PERSON>ncode Confirmation',\n", "                'bot_dialogues_keywords': ['Confirm करने के लिए', 'pincode', 'city का सही pincode', 'mistake हुई है', 'correct pincode क्या है'],\n", "                'expected_human_confirmations': ['yes', 'no', 'that\\'s right', 'incorrect', 'हां', 'हाँ', 'correct', 'yeah'],\n", "                'required_info': ['pincode']\n", "            },\n", "            9: {\n", "                'name': 'Physical Activity Inquiry',\n", "                'bot_dialogues_keywords': ['regular physical activity', 'walking', 'yoga', 'gym', 'wellness routine', 'exercise'],\n", "                'expected_human_confirmations': ['yes', 'no', 'ठीक', 'हां', 'हाँ', 'जी', 'walking', 'working'],\n", "                'required_info': ['physical_activity_status']\n", "            },\n", "            10: {\n", "                'name': 'Handle Personal Details Refusal',\n", "                'bot_dialogues_keywords': ['समझ सकता हूँ', 'details सिर्फ इसलिए पूछने हैं', 'plan recommend', 'needs के साथ better fit', 'private और secure', 'general information share'],\n", "                'expected_human_confirmations': ['yes', 'no', 'ठीक', 'हां', 'हाँ'],\n", "                'required_info': []\n", "            },\n", "            11: {\n", "                'name': 'Pre-existing Conditions Inquiry',\n", "                'bot_dialogues_keywords': ['pre-existing health conditions', 'asthma', 'diabetes', 'blood pressure', 'heart issues', 'जानना चाहिए'],\n", "                'expected_human_confirmations': ['yes', 'no', 'कुछ भी नहीं है', 'हां', 'हाँ', 'nothing'],\n", "                'required_info': ['medical_conditions']\n", "            },\n", "            12: {\n", "                'name': 'Current Medications Inquiry',\n", "                'bot_dialogues_keywords': ['currently किसी health condition के लिए कोई medications ले रहे हैं', 'problem नहीं', 'privacy हमेशा respected और protected'],\n", "                'expected_human_confirmations': ['yes', 'no', 'ठीक', 'हां', 'हाँ', 'never'],\n", "                'required_info': ['medications_status']\n", "            },\n", "            13: {\n", "                'name': 'Hospitalization History Inquiry',\n", "                'bot_dialogues_keywords': ['कभी अस्पताल में भर्ती हुए हैं', 'recently hospitalized', 'कब था'],\n", "                'expected_human_confirmations': ['yes', 'no', 'ठीक', 'हां', 'हाँ', 'never', 'January'], # Added January\n", "                'required_info': ['hospitalization_history']\n", "            },\n", "            14: {\n", "                'name': 'Doctor Recommendation Inquiry',\n", "                'bot_dialogues_keywords': ['doctor ने recently किसी medical condition के लिए surgery या treatment recommend किया है', 'align करे', 'privacy हमारी priority'],\n", "                'expected_human_confirmations': ['yes', 'no', 'ठीक', 'हां', 'हाँ'],\n", "                'required_info': ['doctor_recommendation_status']\n", "            },\n", "            15: {\n", "                'name': 'Policy Suggestion',\n", "                'bot_dialogues_keywords': ['Based on the details', 'policy की सलाह दूंगी', 'specially young और healthy', 'कम premium में ज्यादा rewards', 'अधिक जानने में interested हैं', 'Activ Fit', 'Activ One Vital', 'Activ One Maxplus', 'Activ One N-X-T', 'Activ Health Platinum Enhanced', 'Activ Care Classic'],\n", "                'expected_human_confirmations': ['yes', 'no', 'बोलिए', 'हां', 'हाँ', 'interested'],\n", "                'required_info': ['policy_suggested']\n", "            },\n", "            16: {\n", "                'name': 'Waiting Period Information',\n", "                'bot_dialogues_keywords': ['waiting period', 'thirty days', 'three years', 'two years', 'initial waiting period', 'pre-existing disease waiting period', 'specific disease waiting period', 'Chronic Diseases will be covered from day one', 'email पर मिल जाएंगी'],\n", "                'expected_human_confirmations': ['yes', 'no', 'ठीक', 'हां', 'हाँ'],\n", "                'required_info': []\n", "            },\n", "            17: {\n", "                'name': 'Policy Details Presentation',\n", "                'bot_dialogues_keywords': ['key benefits', 'features', 'Health Returns', 'Claim Protect', 'Super Reload', 'Binge Refill', 'Activ One Maxplus policy', 'Activ Fit policy', 'Activ One Vital plan', 'Activ Care Classic plan', 'Activ One NXT plan', 'Activ Health Platinum Enhanced plan'],\n", "                'expected_human_confirmations': ['yes', 'no', 'ठीक', 'हां', 'हाँ', 'ok'],\n", "                'required_info': []\n", "            },\n", "            18: {\n", "                'name': 'Add-on Coverage Discussion',\n", "                'bot_dialogues_keywords': ['add-on coverages', 'optional', 'broader protection', 'peace of mind', 'Chronic Care', 'Chronic Management Program', 'Durable Equipment Cover', 'Second Medical Opinion', 'Cancer Booster', 'Vaccine Cover', 'Tele-O-P-D Consultation', 'Cashless Anywhere', 'Room Rent'],\n", "                'expected_human_confirmations': ['yes', 'no', 'ठीक', 'हां', 'हाँ'],\n", "                'required_info': ['add_on_interest']\n", "            },\n", "            19: {\n", "                'name': 'Policy Summary and Confirmation',\n", "                'bot_dialogues_keywords': ['summary provide', 'confirm', 'proceed', 'application', 'Health Returns', 'Claim Protect', 'Super Reload', 'Super Credit'],\n", "                'expected_human_confirmations': ['yes', 'no', 'ठीक', 'हां', 'हाँ', 'ji hai'],\n", "                'required_info': ['confirmation_to_proceed_application']\n", "            },\n", "            20: {\n", "                'name': 'Quotation',\n", "                'bot_dialogues_keywords': ['policy', 'Add-on चुन लिए हैं', 'आवेदन शुरू करना है', 'expert टीम से एक callback शेड्यूल', 'sahi premium information'],\n", "                'expected_human_confirmations': ['yes', 'no', 'ठीक', 'हां', 'हाँ'],\n", "                'required_info': ['callback_agreement']\n", "            },\n", "            21: {\n", "                'name': 'Payment and Next Steps',\n", "                'bot_dialogues_keywords': ['payment details', 'discuss', 'team contact', 'questions'],\n", "                'expected_human_confirmations': ['yes', 'no', 'ठीक', 'हां', 'हाँ'],\n", "                'required_info': []\n", "            },\n", "            22: {\n", "                'name': '<PERSON> Callback',\n", "                'bot_dialogues_keywords': ['callback schedule', 'convenient time', 'tomorrow', 'morning', 'evening', 'questions', 'contact'],\n", "                'expected_human_confirmations': ['yes', 'no', 'ठीक', 'हां', 'हाँ', 'ok', 'sure', 'yeah'],\n", "                'required_info': ['callback_time', 'callback_date']\n", "            },\n", "            23: {\n", "                'name': '<PERSON><PERSON> Hesitation',\n", "                'bot_dialogues_keywords': ['important decision', 'time लेना ठीक है', 'call schedule करना सही रहेगा'],\n", "                'expected_human_confirmations': ['yes', 'no', 'ठीक', 'हां', 'हाँ'],\n", "                'required_info': ['willing_to_schedule_callback']\n", "            },\n", "            24: { # Placeholder for Carney Remarks\n", "                'name': 'Carney <PERSON>marks',\n", "                'bot_dialogues_keywords': [],\n", "                'expected_human_confirmations': [],\n", "                'required_info': []\n", "            },\n", "            25: {\n", "                'name': 'Customer Not Interested',\n", "                'bot_dialogues_keywords': ['disinterest show', 'understand', 'interested क्यों नहीं', 'feedback', 'improve', 'benefits', 'health returns', 'hospital stays', 'Super Reload', 'critical illness', 'personal accident cover'],\n", "                'expected_human_confirmations': ['yes', 'no', 'ठीक', 'हां', 'हाँ'],\n", "                'required_info': []\n", "            },\n", "            26: {\n", "                'name': 'Renewal Inquiry',\n", "                'bot_dialogues_keywords': ['policy renew', 'registered policy number', 'registered mobile number', 'renewal team', 'thirty six working hours'],\n", "                'expected_human_confirmations': ['yes', 'no', 'ठीक', 'हां', 'हाँ'],\n", "                'required_info': ['policy_number', 'mobile_number']\n", "            },\n", "            27: {\n", "                'name': '<PERSON>laim Status Inquiry',\n", "                'bot_dialogues_keywords': ['claim status', 'latest update', 'customer care', 'email'],\n", "                'expected_human_confirmations': ['yes', 'no', 'ठीक', 'हां', 'हाँ'],\n", "                'required_info': []\n", "            },\n", "            28: {\n", "                'name': '<PERSON><PERSON> <PERSON> <PERSON>ail Details Request',\n", "                'bot_dialogues_keywords': ['busy', 'details email', 'send', 'key benefits quickly explain'],\n", "                'expected_human_confirmations': ['yes', 'no', 'ठीक', 'हां', 'हाँ'],\n", "                'required_info': ['agreement_to_explain_benefits']\n", "            },\n", "            29: {\n", "                'name': '<PERSON>y - Quick Wrap Up',\n", "                'bot_dialogues_keywords': ['busy', 'unavailable', 'right time', 'quickly wrap up', 'two minutes'],\n", "                'expected_human_confirmations': ['yes', 'no', 'ठीक', 'हां', 'हाँ'],\n", "                'required_info': []\n", "            },\n", "            30: {\n", "                'name': 'Voicemail',\n", "                'bot_dialogues_keywords': ['voicemail', 'call back', 'health plans explore', 'right coverage'],\n", "                'expected_human_confirmations': [],\n", "                'required_info': []\n", "            },\n", "            31: {\n", "                'name': 'Top Up Existing Policy',\n", "                'bot_dialogues_keywords': ['existing policy', 'top-up plan', 'enhance', 'add members', 'सदस्य जोड़ना', 'financial cushion', 'deductible', 'large medical bills', 'renewal', 'claim status'],\n", "                'expected_human_confirmations': ['yes', 'no', 'correct', 'ठीक', 'हां', 'हाँ'],\n", "                'required_info': ['top_up_interest']\n", "            },\n", "            32: {\n", "                'name': 'Wrong Number',\n", "                'bot_dialogues_keywords': ['wrong number', 'concerned person', 'health insurance', 'confirm'],\n", "                'expected_human_confirmations': ['yes', 'no', 'ठीक', 'हां', 'हाँ'],\n", "                'required_info': ['wrong_number_status']\n", "            },\n", "            33: {\n", "                'name': 'Already Received Call',\n", "                'bot_dialogues_keywords': ['contacted', 'full policy presentation', 'check'],\n", "                'expected_human_confirmations': ['yes', 'no', 'ठीक', 'हां', 'हाँ'],\n", "                'required_info': ['presentation_status']\n", "            },\n", "            34: {\n", "                'name': 'Presentation Done',\n", "                'bot_dialogues_keywords': ['policy details', 'purchase', 'proceed'],\n", "                'expected_human_confirmations': ['yes', 'no', 'ठीक', 'हां', 'हाँ'],\n", "                'required_info': ['purchase_intent']\n", "            },\n", "            35: {\n", "                'name': 'Unable to Hear',\n", "                'bot_dialogues_keywords': ['hear', 'audible', 'callback'],\n", "                'expected_human_confirmations': ['yes', 'no', 'ठीक', 'हां', 'हाँ'],\n", "                'required_info': []\n", "            },\n", "            36: {\n", "                'name': 'Not Visited Website',\n", "                'bot_dialogues_keywords': ['benefits', 'health returns', 'hospital stays', 'Super Reload', 'critical illness', 'personal accident cover', 'health insurance तलाश'],\n", "                'expected_human_confirmations': ['yes', 'no', 'ठीक', 'हां', 'हाँ'],\n", "                'required_info': ['interest_in_insurance']\n", "            },\n", "            37: {\n", "                'name': 'Financial Issues',\n", "                'bot_dialogues_keywords': ['budget', 'affordable', 'health returns', 'premium', 'GST', 'non-medical hospital expenses', 'Super Reload', 'critical illness', 'personal accident cover'],\n", "                'expected_human_confirmations': ['yes', 'no', 'ठीक', 'हां', 'हाँ'],\n", "                'required_info': []\n", "            },\n", "            38: {\n", "                'name': 'Payment Push',\n", "                'bot_dialogues_keywords': ['payment process', 'challenges', 'time based offer', 'issues', 'decline'],\n", "                'expected_human_confirmations': ['yes', 'no', 'ठीक', 'हां', 'हाँ'],\n", "                'required_info': []\n", "            },\n", "            39: {\n", "                'name': 'Language Other Than English/Hindi',\n", "                'bot_dialogues_keywords': ['preferred language', 'team contact', 'acceptable'],\n", "                'expected_human_confirmations': ['yes', 'no', 'ठीक', 'हां', 'हाँ'],\n", "                'required_info': ['preferred_language']\n", "            },\n", "            40: {\n", "                'name': '<PERSON>y - WhatsApp Details Request',\n", "                'bot_dialogues_keywords': ['WhatsApp', 'details', 'number', 'confirm', 'key benefits quickly explain'],\n", "                'expected_human_confirmations': ['yes', 'no', 'ठीक', 'हां', 'हाँ'],\n", "                'required_info': ['whatsapp_number_confirmation']\n", "            },\n", "            41: {\n", "                'name': 'Goodbye',\n", "                'bot_dialogues_keywords': ['Goodbye', 'time के लिए धन्यवाद', 'follow up', 'questions', 'संपर्क करें', 'Have a great day'],\n", "                'expected_human_confirmations': [],\n", "                'required_info': []\n", "            }\n", "        }\n", "    }\n", "\n", "    # Ensure all steps from 1 to 41 are in prompt_rules, even if placeholder, to ensure flow tracker works correctly.\n", "    for i in range(1, 42): # Assuming max step is 41\n", "        if i not in prompt_rules['steps']:\n", "            prompt_rules['steps'][i] = {\n", "                'name': f'Placeholder Step {i}',\n", "                'bot_dialogues_keywords': [],\n", "                'expected_human_confirmations': [],\n", "                'required_info': []\n", "            }\n", "\n", "    # Re-sort steps in prompt_rules for consistent access\n", "    prompt_rules['steps'] = dict(sorted(prompt_rules['steps'].items()))\n", "\n", "    # Initialize NLP processor (once for all transcripts)\n", "    nlp_processor = EnhancedNLPProcessor()\n", "\n", "    # Dictionary to store processed data for each customer\n", "    customer_reports_data = {} # Key: customer_name, Value: {'processed_transcript_df', 'call_report_data'}\n", "\n", "    print(\"\\n--- Processing Transcripts ---\")\n", "    for idx, transcript_raw in enumerate(transcripts_raw):\n", "        customer_name = extract_customer_name(transcript_raw)\n", "        print(f\"\\nProcessing call for: {customer_name} (Transcript {idx+1}/{len(transcripts_raw)})\")\n", "\n", "        # Parse the current transcript\n", "        parsed_transcript = []\n", "        current_time = datetime.now()\n", "        for line in transcript_raw.strip().split('\\n'):\n", "            line = line.strip()\n", "            if line.startswith(\"Chat Bot:\"):\n", "                speaker = \"<PERSON><PERSON>\"\n", "                utterance = line.replace(\"Chat <PERSON>:\", \"\").strip()\n", "            elif line.startswith(\"Human:\"):\n", "                speaker = \"Human\"\n", "                utterance = line.replace(\"Human:\", \"\").strip()\n", "            else:\n", "                continue # Skip empty or malformed lines\n", "\n", "            # Filter out <silence> tags\n", "            utterance = utterance.replace(\"<silence>\", \"\").strip()\n", "            if not utterance:\n", "                continue\n", "\n", "            parsed_transcript.append({\n", "                'speaker': speaker,\n", "                'utterance': utterance,\n", "                'timestamp': current_time # Assign a sequential timestamp for plotting\n", "            })\n", "            current_time += timedelta(seconds=5) # Increment time for next turn\n", "\n", "        # Initialize conversation flow tracker for each transcript\n", "        flow_tracker = EnhancedConversationFlowTracker(prompt_rules)\n", "\n", "        processed_transcript_data_single_call = []\n", "\n", "        # Initialize start_time and end_time for call_report_data\n", "        call_start_time = parsed_transcript[0]['timestamp'] if parsed_transcript else datetime.now()\n", "        call_end_time = parsed_transcript[-1]['timestamp'] if parsed_transcript else datetime.now()\n", "\n", "        call_report_data_single_call = {\n", "            'call_id': f'call_{customer_name}_{idx+1}',\n", "            'customer_name': customer_name,\n", "            'start_time': call_start_time,\n", "            'end_time': call_end_time,\n", "            'duration_seconds': (call_end_time - call_start_time).total_seconds() if parsed_transcript else 0,\n", "            'total_turns': len(parsed_transcript),\n", "            'speakers': list(set([t['speaker'] for t in parsed_transcript])),\n", "            'collected_entities': {},\n", "            'sentiment_summary': {},\n", "            'language_summary': {},\n", "            'conversation_flow_data': {},\n", "            'bot_suggested_policy': None\n", "        }\n", "\n", "        for i, turn in enumerate(parsed_transcript):\n", "            speaker = turn['speaker']\n", "            utterance = turn['utterance']\n", "            turn_index = i + 1\n", "\n", "            extracted_entities = nlp_processor.extract_entities_advanced(utterance, speaker)\n", "\n", "            sentiment_analysis = nlp_processor.analyze_sentiment_advanced(utterance)\n", "            language_detection = nlp_processor.detect_language_advanced(utterance)\n", "\n", "            # Update conversation state\n", "            flow_tracker.update_conversation_state(speaker, utterance, extracted_entities, turn_index)\n", "\n", "            # Store data for this turn\n", "            processed_turn = {\n", "                'turn_index': turn_index,\n", "                'speaker': speaker,\n", "                'utterance': utterance,\n", "                'timestamp': turn['timestamp'],\n", "                'extracted_entities': extracted_entities,\n", "                'sentiment_compound': sentiment_analysis['compound'],\n", "                'sentiment_emotion': sentiment_analysis['emotion'],\n", "                'language': language_detection['language'],\n", "                'language_confidence': language_detection['confidence'],\n", "                'current_flow_step': flow_tracker.conversation_state['current_step']\n", "            }\n", "            processed_transcript_data_single_call.append(processed_turn)\n", "\n", "        # After processing all turns, finalize call report data for this single call\n", "        call_report_data_single_call['conversation_flow_data'] = flow_tracker.get_conversation_flow_data()\n", "        call_report_data_single_call['collected_entities'] = flow_tracker.conversation_state['collected_entities']\n", "\n", "        # Summarize sentiment and language from processed_transcript_data\n", "        df_transcript_single_call = pd.DataFrame(processed_transcript_data_single_call)\n", "\n", "        if not df_transcript_single_call.empty:\n", "            call_report_data_single_call['sentiment_summary'] = {\n", "                'avg_compound_human': df_transcript_single_call[df_transcript_single_call['speaker'] == 'Human']['sentiment_compound'].mean(),\n", "                'avg_compound_bot': df_transcript_single_call[df_transcript_single_call['speaker'] == 'Chat <PERSON>']['sentiment_compound'].mean(),\n", "                'overall_emotion_counts': df_transcript_single_call['sentiment_emotion'].value_counts().to_dict()\n", "            }\n", "            call_report_data_single_call['language_summary'] = {\n", "                'language_counts_human': df_transcript_single_call[df_transcript_single_call['speaker'] == 'Human']['language'].value_counts().to_dict(),\n", "                'language_counts_bot': df_transcript_single_call[df_transcript_single_call['speaker'] == 'Chat Bot']['language'].value_counts().to_dict(),\n", "                'total_language_counts': df_transcript_single_call['language'].value_counts().to_dict()\n", "            }\n", "\n", "            # Attempt to derive bot suggested policy for THIS CALL\n", "            # Look for specific phrasing in Step 15 where bot suggests a policy\n", "            bot_suggested_policies_current_call = [] # List for policies suggested in THIS call\n", "            for t in processed_transcript_data_single_call:\n", "                if t['speaker'] == 'Chat Bo<PERSON>' and t['current_flow_step'] == 15:\n", "                    # Check for explicit suggestion phrasing from the prompt: \"मैं \"Activ Fit\" policy की सलाह दूंगी.\"\n", "                    suggestion_match = re.search(r'मैं \"(Activ Fit|Activ One Maxplus|Activ One Vital|Activ One N-X-T|Activ Health Platinum Enhanced|Activ Care Classic)\" policy की सलाह दूंगी', t['utterance'], re.IGNORECASE)\n", "                    if suggestion_match:\n", "                        bot_suggested_policies_current_call.append(suggestion_match.group(1))\n", "\n", "                    # Also check for Activ One V-Y-T-L policy which might be suggested directly by name in the text\n", "                    # Example: \"मैं आपको Activ One V-Y-T-L policy की सिफारिश कर सकता हूँ\"\n", "                    elif \"Activ One V-Y-T-L\" in t['utterance'] or \"Activ One Vital\" in t['utterance']: # Ensure both forms are caught\n", "                        if \"Activ One Vital\" not in bot_suggested_policies_current_call: # Avoid duplicates\n", "                            bot_suggested_policies_current_call.append(\"Activ One Vital\")\n", "\n", "                    # If no exact match, check if any recognized policy name is mentioned in the utterance\n", "                    for policy_name_in_ent in t['extracted_entities'].get('policies_mentioned', []):\n", "                        if policy_name_in_ent not in bot_suggested_policies_current_call:\n", "                            bot_suggested_policies_current_call.append(policy_name_in_ent)\n", "\n", "            # Only set bot_suggested_policy if a unique policy was clearly suggested\n", "            if bot_suggested_policies_current_call:\n", "                call_report_data_single_call['bot_suggested_policy'] = bot_suggested_policies_current_call[0] # Take the first suggested policy\n", "\n", "            # Store data for the current customer\n", "            customer_reports_data[customer_name] = {\n", "                'processed_transcript_df': df_transcript_single_call,\n", "                'call_report_data': call_report_data_single_call\n", "            }\n", "\n", "    # Instantiate and use the dashboard with all customer data\n", "    dashboard = EnhancedABHIVisualizationDashboard(customer_reports_data, nlp_processor, prompt_rules)\n", "\n", "    # Generate and show plots\n", "    print(\"\\n📊 Generating Combined Visualizations...\")\n", "\n", "    fig_heatmap = dashboard.create_enhanced_conversation_flow_heatmap()\n", "    fig_heatmap.show()\n", "\n", "    fig_policy_chart = dashboard.create_enhanced_policy_entity_chart()\n", "    fig_policy_chart.show()\n", "\n", "    fig_sentiment_time = dashboard.create_sentiment_over_time_chart()\n", "    fig_sentiment_time.show()\n", "\n", "    fig_language_dist = dashboard.create_language_distribution_chart()\n", "    fig_language_dist.show()\n", "\n", "    fig_entity_overview = dashboard.create_entity_extraction_overview()\n", "    fig_entity_overview.show()\n", "\n", "    fig_step_timeline = dashboard.create_step_completion_timeline()\n", "    fig_step_timeline.show()\n", "\n", "    # Generate and print Prompt Adherence and Correctness Analysis\n", "    print(\"\\n--- Prompt Adherence and Correctness Analysis ---\")\n", "    adherence_df = dashboard.analyze_prompt_adherence_and_correctness()\n", "    print(adherence_df.to_markdown(index=False)) # Use to_markdown for nice console output\n", "\n", "    print(\"\\n--- Summary of Collected Entities and Bot Suggestions Per Call ---\")\n", "    for customer_name, data in customer_reports_data.items():\n", "        print(f\"\\nCustomer: {customer_name}\")\n", "        print(f\"  Collected Entities: {json.dumps(data['call_report_data']['collected_entities'], indent=2, ensure_ascii=False)}\")\n", "        print(f\"  Bot Suggested Policy: {data['call_report_data']['bot_suggested_policy']}\")\n", "        print(f\"  Final Flow State: Current Step {data['call_report_data']['conversation_flow_data']['current_step']}, Completed Steps {data['call_report_data']['conversation_flow_data']['completed_steps']}\")\n", "\n", "\n"]}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 0}