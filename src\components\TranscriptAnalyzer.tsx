import React, { useState, useEffect } from 'react';
import { Upload, FileText, Bar<PERSON>hart3, TrendingUp, Check<PERSON>ircle, XCircle, AlertTriangle } from 'lucide-react';

interface CustomerData {
  name: string;
  age?: number;
  pincode?: string;
  persons?: string[];
  health_conditions?: string[];
  language?: string;
  [key: string]: any;
}

interface PolicyAnalysis {
  expected: string;
  actual: string;
  match: boolean;
  confidence: number;
}

interface StepStatus {
  stepNumber: number;
  stepName: string;
  status: 'Followed' | 'Skipped' | 'Missing' | 'Out-of-Order';
  timestamp?: string;
}

interface ConversationFlow {
  path: number[];
  stepNames: string[];
  completionRate: number;
  adherenceScore: number;
}

interface CallAnalysis {
  customerName: string;
  collectedEntities: CustomerData;
  policyAnalysis: PolicyAnalysis;
  conversationFlow: ConversationFlow;
  stepStatuses: StepStatus[];
  outcome: 'Success' | 'Partial' | 'Failed';
  duration?: number;
  call_quality?: string;
  language?: string;
}

interface AnalysisResults {
  totalCalls: number;
  successRate: number;
  averageAdherence: number;
  calls: CallAnalysis[];
  aggregatedMetrics: {
    stepCompletionRates: { [key: number]: number };
    commonPaths: string[];
    policyAccuracy: number;
  };
  language_analysis?: { [key: string]: number };
  quality_analysis?: { [key: string]: number };
}

const TranscriptAnalyzer: React.FC = () => {
  const [analysisResults, setAnalysisResults] = useState<AnalysisResults | null>(null);
  const [loading, setLoading] = useState(false);
  const [selectedView, setSelectedView] = useState<'overview' | 'individual' | 'flows' | 'policies'>('overview');
  const [uploadMode, setUploadMode] = useState<'file' | 'text'>('file');
  const [transcriptText, setTranscriptText] = useState('');
  const [uploadMessage, setUploadMessage] = useState('');

  useEffect(() => {
    // Load analysis results from API if available
    loadAnalysisResults();
  }, []);

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files || files.length === 0) return;

    setLoading(true);
    setUploadMessage('');
    
    try {
      const formData = new FormData();
      formData.append('file', files[0]);

      const response = await fetch('http://localhost:5000/api/upload-transcript', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Upload failed');
      }

      const uploadData = await response.json();
      setUploadMessage(uploadData.message);
      
      // Reload analysis results to include the new transcript
      await loadAnalysisResults();
    } catch (error) {
      console.error('Error uploading file:', error);
      setUploadMessage(`Error uploading transcript: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  };

  const handleTextUpload = async () => {
    if (!transcriptText.trim()) {
      setUploadMessage('Please enter transcript text');
      return;
    }

    setLoading(true);
    setUploadMessage('');
    
    try {
      const response = await fetch('http://localhost:5000/api/upload-transcript', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ transcript_text: transcriptText }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Upload failed');
      }

      const uploadData = await response.json();
      setUploadMessage(uploadData.message);
      setTranscriptText('');
      
      // Reload analysis results to include the new transcript
      await loadAnalysisResults();
    } catch (error) {
      console.error('Error uploading text:', error);
      setUploadMessage(`Error uploading transcript: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  };

  const loadAnalysisResults = async () => {
    try {
      const response = await fetch('http://localhost:5000/api/analyze', {
        method: 'GET',
      });
      
      if (response.ok) {
        const data = await response.json();
        setAnalysisResults(data);
      }
    } catch (error) {
      console.log('No existing analysis results found');
    }
  };

  const renderOverview = () => (
    <div className="space-y-6">
      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white p-6 rounded-lg shadow-md border-l-4 border-blue-500">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Calls</p>
              <p className="text-2xl font-bold text-gray-900">{analysisResults?.totalCalls}</p>
            </div>
            <FileText className="h-8 w-8 text-blue-500" />
          </div>
        </div>
        
        <div className="bg-white p-6 rounded-lg shadow-md border-l-4 border-green-500">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Success Rate</p>
              <p className="text-2xl font-bold text-gray-900">{analysisResults?.successRate}%</p>
            </div>
            <CheckCircle className="h-8 w-8 text-green-500" />
          </div>
        </div>
        
        <div className="bg-white p-6 rounded-lg shadow-md border-l-4 border-purple-500">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Avg Adherence</p>
              <p className="text-2xl font-bold text-gray-900">{analysisResults?.averageAdherence}%</p>
            </div>
            <TrendingUp className="h-8 w-8 text-purple-500" />
          </div>
        </div>
        
        <div className="bg-white p-6 rounded-lg shadow-md border-l-4 border-orange-500">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Policy Accuracy</p>
              <p className="text-2xl font-bold text-gray-900">{analysisResults?.aggregatedMetrics.policyAccuracy}%</p>
            </div>
            <BarChart3 className="h-8 w-8 text-orange-500" />
          </div>
        </div>
      </div>

      {/* Step Completion Heatmap */}
      <div className="bg-white p-6 rounded-lg shadow-md">
        <h3 className="text-lg font-semibold mb-4">Step Completion Heatmap</h3>
        <div className="grid grid-cols-10 gap-1">
          {Object.entries(analysisResults?.aggregatedMetrics.stepCompletionRates || {}).map(([step, rate]) => (
            <div
              key={step}
              className={`h-8 w-8 rounded flex items-center justify-center text-xs font-medium text-white ${
                rate >= 90 ? 'bg-green-500' :
                rate >= 70 ? 'bg-yellow-500' :
                rate >= 50 ? 'bg-orange-500' : 'bg-red-500'
              }`}
              title={`Step ${step}: ${rate}%`}
            >
              {step}
            </div>
          ))}
        </div>
        <div className="mt-2 flex items-center space-x-4 text-sm text-gray-600">
          <div className="flex items-center">
            <div className="w-3 h-3 bg-green-500 rounded mr-1"></div>
            <span>90-100%</span>
          </div>
          <div className="flex items-center">
            <div className="w-3 h-3 bg-yellow-500 rounded mr-1"></div>
            <span>70-89%</span>
          </div>
          <div className="flex items-center">
            <div className="w-3 h-3 bg-orange-500 rounded mr-1"></div>
            <span>50-69%</span>
          </div>
          <div className="flex items-center">
            <div className="w-3 h-3 bg-red-500 rounded mr-1"></div>
            <span>&lt;50%</span>
          </div>
        </div>
      </div>

      {/* Language and Quality Analysis */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {analysisResults?.language_analysis && (
          <div className="bg-white p-6 rounded-lg shadow-md">
            <h3 className="text-lg font-semibold mb-4">Language Distribution</h3>
            <div className="space-y-3">
              {Object.entries(analysisResults.language_analysis).map(([lang, count]) => (
                <div key={lang} className="flex items-center justify-between">
                  <span className="text-sm font-medium">{lang}</span>
                  <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm font-medium">
                    {count} calls
                  </span>
                </div>
              ))}
            </div>
          </div>
        )}
        
        {analysisResults?.quality_analysis && (
          <div className="bg-white p-6 rounded-lg shadow-md">
            <h3 className="text-lg font-semibold mb-4">Call Quality Distribution</h3>
            <div className="space-y-3">
              {Object.entries(analysisResults.quality_analysis).map(([quality, count]) => (
                <div key={quality} className="flex items-center justify-between">
                  <span className="text-sm font-medium">{quality}</span>
                  <span className={`px-2 py-1 rounded text-sm font-medium ${
                    quality === 'Excellent' ? 'bg-blue-100 text-blue-800' :
                    quality === 'Good' ? 'bg-green-100 text-green-800' :
                    'bg-red-100 text-red-800'
                  }`}>
                    {count} calls
                  </span>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Common Conversation Paths */}
      <div className="bg-white p-6 rounded-lg shadow-md">
        <h3 className="text-lg font-semibold mb-4">Most Common Conversation Paths</h3>
        <div className="space-y-3">
          {analysisResults?.aggregatedMetrics.commonPaths.map((path, index) => (
            <div key={index} className="flex items-center space-x-2">
              <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm font-medium">
                #{index + 1}
              </span>
              <span className="font-mono text-sm bg-gray-100 px-3 py-1 rounded">{path}</span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  const renderIndividualCalls = () => (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold">Individual Call Analysis</h3>
      {analysisResults?.calls.map((call, index) => (
        <div key={index} className="bg-white p-6 rounded-lg shadow-md">
          <div className="flex items-center justify-between mb-4">
            <h4 className="text-lg font-medium">Customer: {call.customerName}</h4>
            <div className="flex items-center space-x-2">
              <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                call.outcome === 'Success' ? 'bg-green-100 text-green-800' :
                call.outcome === 'Partial' ? 'bg-yellow-100 text-yellow-800' :
                'bg-red-100 text-red-800'
              }`}>
                {call.outcome}
              </span>
              {call.call_quality && (
                <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                  call.call_quality === 'Excellent' ? 'bg-blue-100 text-blue-800' :
                  call.call_quality === 'Good' ? 'bg-green-100 text-green-800' :
                  'bg-red-100 text-red-800'
                }`}>
                  {call.call_quality}
                </span>
              )}
              {call.language && (
                <span className="px-3 py-1 rounded-full text-sm font-medium bg-purple-100 text-purple-800">
                  {call.language}
                </span>
              )}
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h5 className="font-medium mb-2">Collected Entities</h5>
              <div className="bg-gray-50 p-3 rounded text-sm">
                <pre>{JSON.stringify(call.collectedEntities, null, 2)}</pre>
              </div>
            </div>
            
            <div>
              <h5 className="font-medium mb-2">Policy Analysis</h5>
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <span className="text-sm text-gray-600">Expected:</span>
                  <span className="font-medium">{call.policyAnalysis.expected}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="text-sm text-gray-600">Actual:</span>
                  <span className="font-medium">{call.policyAnalysis.actual}</span>
                </div>
                <div className="flex items-center space-x-2">
                  {call.policyAnalysis.match ? (
                    <CheckCircle className="h-4 w-4 text-green-500" />
                  ) : (
                    <XCircle className="h-4 w-4 text-red-500" />
                  )}
                  <span className={`text-sm font-medium ${
                    call.policyAnalysis.match ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {call.policyAnalysis.match ? 'Match' : 'Mismatch'}
                  </span>
                </div>
              </div>
            </div>
          </div>
          
          <div className="mt-4">
            <h5 className="font-medium mb-2">Conversation Flow</h5>
            <div className="flex items-center space-x-2 text-sm">
              {call.conversationFlow.stepNames.map((step, idx) => (
                <React.Fragment key={idx}>
                  <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded">{step}</span>
                  {idx < call.conversationFlow.stepNames.length - 1 && (
                    <span className="text-gray-400">→</span>
                  )}
                </React.Fragment>
              ))}
            </div>
            <div className="mt-2 flex space-x-4 text-sm text-gray-600">
              <span>Completion: {call.conversationFlow.completionRate}%</span>
              <span>Adherence: {call.conversationFlow.adherenceScore}%</span>
            </div>
          </div>
        </div>
      ))}
    </div>
  );

  const renderFlowAnalysis = () => (
    <div className="space-y-6">
      <h3 className="text-lg font-semibold">Conversation Flow Analysis</h3>
      
      {/* Flow Visualization */}
      <div className="bg-white p-6 rounded-lg shadow-md">
        <h4 className="font-medium mb-4">Step Completion Timeline</h4>
        <div className="space-y-4">
          {analysisResults?.calls.map((call, index) => (
            <div key={index} className="border-l-2 border-gray-200 pl-4">
              <div className="flex items-center space-x-2 mb-2">
                <span className="font-medium">{call.customerName}</span>
                <span className="text-sm text-gray-500">
                  ({call.conversationFlow.completionRate}% complete)
                </span>
              </div>
              <div className="flex flex-wrap gap-1">
                {call.conversationFlow.path.map((step, stepIndex) => (
                  <div
                    key={stepIndex}
                    className="w-6 h-6 rounded-full bg-green-500 text-white text-xs flex items-center justify-center"
                    title={`Step ${step}`}
                  >
                    {step}
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Step Status Summary */}
      <div className="bg-white p-6 rounded-lg shadow-md">
        <h4 className="font-medium mb-4">Step Adherence Summary</h4>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Customer
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Followed
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Skipped
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Out of Order
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Adherence Score
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {analysisResults?.calls.map((call, index) => {
                const followed = call.stepStatuses.filter(s => s.status === 'Followed').length;
                const skipped = call.stepStatuses.filter(s => s.status === 'Skipped').length;
                const outOfOrder = call.stepStatuses.filter(s => s.status === 'Out-of-Order').length;
                
                return (
                  <tr key={index}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {call.customerName}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-green-600">
                      {followed}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-yellow-600">
                      {skipped}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-red-600">
                      {outOfOrder}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      <div className="flex items-center">
                        <span className="mr-2">{call.conversationFlow.adherenceScore}%</span>
                        <div className="w-16 bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-blue-600 h-2 rounded-full"
                            style={{ width: `${call.conversationFlow.adherenceScore}%` }}
                          ></div>
                        </div>
                      </div>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );

  const renderPolicyAnalysis = () => (
    <div className="space-y-6">
      <h3 className="text-lg font-semibold">Policy Suggestion Analysis</h3>
      
      <div className="bg-white p-6 rounded-lg shadow-md">
        <h4 className="font-medium mb-4">Policy Match/Mismatch Summary</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h5 className="text-sm font-medium text-gray-700 mb-3">Match Rate by Customer</h5>
            <div className="space-y-2">
              {analysisResults?.calls.map((call, index) => (
                <div key={index} className="flex items-center justify-between">
                  <span className="text-sm">{call.customerName}</span>
                  <div className="flex items-center space-x-2">
                    {call.policyAnalysis.match ? (
                      <CheckCircle className="h-4 w-4 text-green-500" />
                    ) : (
                      <XCircle className="h-4 w-4 text-red-500" />
                    )}
                    <span className="text-sm font-medium">
                      {(call.policyAnalysis.confidence * 100).toFixed(1)}%
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
          
          <div>
            <h5 className="text-sm font-medium text-gray-700 mb-3">Policy Distribution</h5>
            <div className="space-y-2">
              {/* This would show actual policy distribution from your data */}
              <div className="flex items-center justify-between">
                <span className="text-sm">Activ Fit</span>
                <span className="text-sm font-medium">4 calls</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Health Plus</span>
                <span className="text-sm font-medium">3 calls</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Basic Care</span>
                <span className="text-sm font-medium">2 calls</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="bg-white p-6 rounded-lg shadow-md">
        <h4 className="font-medium mb-4">Detailed Policy Analysis</h4>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Customer
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Expected Policy
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actual Policy
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Match
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Confidence
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {analysisResults?.calls.map((call, index) => (
                <tr key={index}>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    {call.customerName}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {call.policyAnalysis.expected}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {call.policyAnalysis.actual}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm">
                    {call.policyAnalysis.match ? (
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        <CheckCircle className="h-3 w-3 mr-1" />
                        Match
                      </span>
                    ) : (
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                        <XCircle className="h-3 w-3 mr-1" />
                        Mismatch
                      </span>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {(call.policyAnalysis.confidence * 100).toFixed(1)}%
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Transcript Analysis Dashboard
          </h1>
          <p className="text-gray-600">
            Comprehensive analysis of voice bot conversations with policy accuracy tracking
          </p>
        </div>

        {/* Upload Section */}
        <div className="bg-white p-6 rounded-lg shadow-md mb-6">
          <h3 className="text-lg font-semibold mb-4">Add New Transcript</h3>
          
          {/* Upload Mode Toggle */}
          <div className="flex space-x-4 mb-4">
            <button
              onClick={() => setUploadMode('file')}
              className={`px-4 py-2 rounded-md font-medium ${
                uploadMode === 'file'
                  ? 'bg-blue-500 text-white'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
            >
              Upload File
            </button>
            <button
              onClick={() => setUploadMode('text')}
              className={`px-4 py-2 rounded-md font-medium ${
                uploadMode === 'text'
                  ? 'bg-blue-500 text-white'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
            >
              Paste Text
            </button>
          </div>

          {/* File Upload */}
          {uploadMode === 'file' && (
            <div className="flex items-center space-x-4">
              <Upload className="h-6 w-6 text-gray-400" />
              <div className="flex-1">
                <label htmlFor="file-upload" className="cursor-pointer">
                  <span className="text-sm font-medium text-gray-700">
                    Upload transcript file (PDF, TXT, DOCX)
                  </span>
                  <input
                    id="file-upload"
                    type="file"
                    accept=".pdf,.txt,.docx"
                    onChange={handleFileUpload}
                    className="hidden"
                  />
                </label>
              </div>
            </div>
          )}

          {/* Text Input */}
          {uploadMode === 'text' && (
            <div className="space-y-4">
              <textarea
                value={transcriptText}
                onChange={(e) => setTranscriptText(e.target.value)}
                placeholder="Paste your transcript text here..."
                className="w-full h-32 p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
              <button
                onClick={handleTextUpload}
                disabled={loading || !transcriptText.trim()}
                className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:bg-gray-400 disabled:cursor-not-allowed"
              >
                Add Transcript
              </button>
            </div>
          )}

          {/* Loading and Message */}
          {(loading || uploadMessage) && (
            <div className="mt-4 flex items-center space-x-2">
              {loading && (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                  <span className="text-sm text-gray-600">Processing...</span>
                </>
              )}
              {uploadMessage && (
                <span className={`text-sm ${uploadMessage.includes('Error') ? 'text-red-600' : 'text-green-600'}`}>
                  {uploadMessage}
                </span>
              )}
            </div>
          )}
        </div>

        {/* Navigation Tabs */}
        {analysisResults && (
          <div className="mb-6">
            <nav className="flex space-x-8">
              {[
                { key: 'overview', label: 'Overview', icon: BarChart3 },
                { key: 'individual', label: 'Individual Calls', icon: FileText },
                { key: 'flows', label: 'Flow Analysis', icon: TrendingUp },
                { key: 'policies', label: 'Policy Analysis', icon: CheckCircle },
              ].map(({ key, label, icon: Icon }) => (
                <button
                  key={key}
                  onClick={() => setSelectedView(key as any)}
                  className={`flex items-center space-x-2 py-2 px-4 border-b-2 font-medium text-sm ${
                    selectedView === key
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <Icon className="h-4 w-4" />
                  <span>{label}</span>
                </button>
              ))}
            </nav>
          </div>
        )}

        {/* Content */}
        {analysisResults && (
          <div>
            {selectedView === 'overview' && renderOverview()}
            {selectedView === 'individual' && renderIndividualCalls()}
            {selectedView === 'flows' && renderFlowAnalysis()}
            {selectedView === 'policies' && renderPolicyAnalysis()}
          </div>
        )}

        {!analysisResults && !loading && (
          <div className="text-center py-12">
            <FileText className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No analysis data</h3>
            <p className="mt-1 text-sm text-gray-500">
              Upload transcript files to begin analysis
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default TranscriptAnalyzer;