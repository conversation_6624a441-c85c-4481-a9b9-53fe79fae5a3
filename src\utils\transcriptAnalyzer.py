"""
Enhanced Transcript Analysis System for Aditya Birla Health Insurance
Comprehensive analysis of voice bot conversations with policy accuracy tracking
"""

import json
import re
from typing import Dict, List, Tuple, Any, Optional
from dataclasses import dataclass, asdict
from datetime import datetime
from pathlib import Path
import PyPDF2
import docx
from collections import defaultdict, Counter

@dataclass
class CustomerData:
    """Structure for customer information collected during conversation"""
    name: str
    age: Optional[int] = None
    pincode: Optional[str] = None
    persons: Optional[List[str]] = None
    income: Optional[str] = None
    health_conditions: Optional[List[str]] = None
    family_size: Optional[int] = None
    policy_type: Optional[str] = None
    language: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        return {k: v for k, v in asdict(self).items() if v is not None}

@dataclass
class PolicyAnalysis:
    """Analysis of policy suggestions"""
    expected: str
    actual: str
    match: bool
    confidence: float
    reasoning: str = ""
    policy_features: List[str] = None

@dataclass
class StepStatus:
    """Status of individual conversation steps"""
    step_number: int
    step_name: str
    status: str  # 'Followed', 'Skipped', 'Missing', 'Out-of-Order'
    timestamp: Optional[str] = None
    content: Optional[str] = None

@dataclass
class ConversationFlow:
    """Analysis of conversation flow and adherence"""
    path: List[int]
    step_names: List[str]
    completion_rate: float
    adherence_score: float
    total_steps: int
    skipped_steps: List[int]

@dataclass
class CallAnalysis:
    """Complete analysis of a single call"""
    customer_name: str
    collected_entities: CustomerData
    policy_analysis: PolicyAnalysis
    conversation_flow: ConversationFlow
    step_statuses: List[StepStatus]
    outcome: str  # 'Success', 'Partial', 'Failed'
    duration: Optional[int] = None
    transcript_text: str = ""
    call_quality: str = "Good"  # Good, Poor, Excellent
    language_mix: str = "English"  # English, Hindi, Mixed

class AdityaBirlaTranscriptParser:
    """Specialized parser for Aditya Birla Health Insurance transcripts"""
    
    def __init__(self):
        self.entity_patterns = self._compile_entity_patterns()
        self.step_patterns = self._compile_step_patterns()
        self.policy_patterns = self._compile_policy_patterns()
        self.language_patterns = self._compile_language_patterns()
        
    def _compile_entity_patterns(self) -> Dict[str, re.Pattern]:
        """Compile regex patterns for entity extraction from Aditya Birla transcripts"""
        return {
            'name': re.compile(r'(?:क्या मैं|am I speaking to|is this)\s+([A-Za-z\s]+?)(?:\s+से बात कर रहा|from|to)', re.IGNORECASE),
            'age': re.compile(r'(?:age|उम्र|years old|yr old)\s*(?:is|:)?\s*(\d{1,3})', re.IGNORECASE),
            'pincode': re.compile(r'(?:pincode|pin code|postal code|zip)\s*(?:is|:)?\s*(\d{6})', re.IGNORECASE),
            'family_size': re.compile(r'(?:family|members|people|family members)\s*(?:size|count)?\s*(?:is|:)?\s*(\d+)', re.IGNORECASE),
            'health_condition': re.compile(r'(?:diabetes|asthma|BP|blood pressure|heart|kidney|stone|chronic)', re.IGNORECASE),
            'income': re.compile(r'(?:income|salary|earn)\s*(?:is|:)?\s*(?:rs\.?|rupees?)?\s*(\d+(?:,\d+)*(?:\.\d+)?)', re.IGNORECASE)
        }
    
    def _compile_step_patterns(self) -> Dict[int, re.Pattern]:
        """Compile patterns to identify conversation steps for Aditya Birla"""
        return {
            1: re.compile(r'(?:नमस्ते|hello|hi|greeting)', re.IGNORECASE),
            2: re.compile(r'(?:recording|quality|monitoring)\s+(?:purpose|के लिए)', re.IGNORECASE),
            3: re.compile(r'(?:fresh policy|new policy|existing policy|port)\s+(?:की तलाश|के बारे में)', re.IGNORECASE),
            4: re.compile(r'(?:coverage|family members|include)\s+(?:चाहते|करना)', re.IGNORECASE),
            5: re.compile(r'(?:age|उम्र|years old)\s+(?:जान|collect)', re.IGNORECASE),
            6: re.compile(r'(?:pincode|location|city|address)', re.IGNORECASE),
            7: re.compile(r'(?:physical activity|exercise|walking|yoga|gym)', re.IGNORECASE),
            8: re.compile(r'(?:health conditions|pre-existing|diabetes|asthma|BP|health assessment)', re.IGNORECASE),
            9: re.compile(r'(?:medications|hospitalized|surgery|treatment|medical history)', re.IGNORECASE),
            10: re.compile(r'(?:recommend|suggest|policy|plan)\s+(?:कर|suggest)', re.IGNORECASE),
            11: re.compile(r'(?:benefits|features|coverage details|explain)', re.IGNORECASE),
            12: re.compile(r'(?:premium|cost|price|amount|charge)', re.IGNORECASE),
            13: re.compile(r'(?:waiting period|terms|conditions)', re.IGNORECASE),
            14: re.compile(r'(?:application|proceed|next steps|process)', re.IGNORECASE),
            15: re.compile(r'(?:goodbye|bye|thank you|धन्यवाद)\s*$', re.IGNORECASE)
        }
    
    def _compile_policy_patterns(self) -> Dict[str, re.Pattern]:
        """Compile patterns to detect Aditya Birla policy mentions"""
        return {
            'activ_fit': re.compile(r'activ\s*fit', re.IGNORECASE),
            'activ_one_vital': re.compile(r'activ\s*one\s*vital', re.IGNORECASE),
            'activ_one_maxplus': re.compile(r'activ\s*one\s*maxplus', re.IGNORECASE),
            'activ_one_vytl': re.compile(r'activ\s*one\s*v[-\s]*y[-\s]*t[-\s]*l', re.IGNORECASE),
            'basic_care': re.compile(r'basic\s*care', re.IGNORECASE),
            'family_health': re.compile(r'family\s*health', re.IGNORECASE)
        }
    
    def _compile_language_patterns(self) -> Dict[str, re.Pattern]:
        """Compile patterns to detect language usage"""
        return {
            'hindi': re.compile(r'[क-ह]', re.UNICODE),
            'english': re.compile(r'[a-zA-Z]'),
            'mixed': re.compile(r'[क-ह].*[a-zA-Z]|[a-zA-Z].*[क-ह]', re.UNICODE)
        }
    
    def extract_customer_data(self, transcript: str) -> CustomerData:
        """Extract customer information from Aditya Birla transcript"""
        data = {}
        
        # Extract name from the greeting pattern - multiple patterns
        name_patterns = [
            r'क्या मैं\s+([A-Za-z\s]+?)\s+से बात कर रहा',
            r'क्या मैं\s+([A-Za-z\s]+?)\s+से बात कर रही हूँ',
            r'am I speaking to\s+([A-Za-z\s]+)',
            r'is this\s+([A-Za-z\s]+)',
            r'क्या मैं\s+([A-Za-z\s]+?)\s+से बात कर रहा हूँ'
        ]
        
        for pattern in name_patterns:
            name_match = re.search(pattern, transcript, re.IGNORECASE)
            if name_match:
                data['name'] = name_match.group(1).strip()
                break
        
        # Extract basic entities
        for entity, pattern in self.entity_patterns.items():
            if entity == 'name' and 'name' in data:
                continue  # Skip if we already found name
                
            matches = pattern.findall(transcript)
            if matches:
                if entity == 'age':
                    data[entity] = int(matches[0])
                elif entity == 'family_size':
                    data[entity] = int(matches[0])
                elif entity == 'health_condition':
                    if 'health_conditions' not in data:
                        data['health_conditions'] = []
                    data['health_conditions'].extend(matches)
                else:
                    data[entity] = matches[0]
        
        # Extract family members mentioned
        family_patterns = [
            r'(?:wife|husband|son|daughter|children|बच्चा|पत्नी)',
            r'(?:मेरी wife|my wife|मेरे husband|my husband)'
        ]
        
        persons = []
        for pattern in family_patterns:
            matches = re.findall(pattern, transcript, re.IGNORECASE)
            persons.extend(matches)
        
        if persons:
            data['persons'] = list(set(persons))
        
        # Determine language
        language = self._detect_language(transcript)
        data['language'] = language
        
        # Create CustomerData object
        customer_name = data.get('name', 'Unknown')
        return CustomerData(
            name=customer_name,
            age=data.get('age'),
            pincode=data.get('pincode'),
            persons=data.get('persons'),
            income=data.get('income'),
            health_conditions=data.get('health_conditions'),
            family_size=data.get('family_size'),
            language=language
        )
    
    def _detect_language(self, transcript: str) -> str:
        """Detect the primary language used in the transcript"""
        hindi_chars = len(re.findall(r'[क-ह]', transcript, re.UNICODE))
        english_chars = len(re.findall(r'[a-zA-Z]', transcript))
        
        if hindi_chars > english_chars:
            return "Hindi"
        elif english_chars > hindi_chars:
            return "English"
        else:
            return "Mixed"
    
    def analyze_conversation_flow(self, transcript: str) -> ConversationFlow:
        """Analyze the conversation flow and step adherence for Aditya Birla"""
        # Split transcript into segments
        segments = transcript.split('\n')
        
        detected_steps = []
        step_order = []
        
        # Analyze each segment to detect steps with context awareness
        for i, segment in enumerate(segments):
            if not segment.strip():
                continue
                
            for step_num, pattern in self.step_patterns.items():
                if pattern.search(segment):
                    # Additional context validation for certain steps
                    if step_num == 15:  # Closing step
                        # Only count as closing if it's near the end or clearly a closing
                        if i >= len(segments) - 5 or re.search(r'(?:goodbye|bye)\s*$', segment, re.IGNORECASE):
                            detected_steps.append(step_num)
                            step_order.append((step_num, i))
                    elif step_num == 1:  # Greeting step
                        # Only count as greeting if it's near the beginning
                        if i <= 5 or re.search(r'(?:नमस्ते|hello|hi)', segment, re.IGNORECASE):
                            detected_steps.append(step_num)
                            step_order.append((step_num, i))
                    else:
                        detected_steps.append(step_num)
                        step_order.append((step_num, i))
        
        # Remove duplicates while preserving order
        unique_steps = []
        seen = set()
        for step in detected_steps:
            if step not in seen:
                unique_steps.append(step)
                seen.add(step)
        
        # Filter out closing step if it appears too early in the conversation
        # Only include closing if it's one of the last few steps or if it's the only step detected
        filtered_steps = []
        for i, step in enumerate(unique_steps):
            if step == 15:  # Closing step
                # Only include if it's one of the last 3 steps or if very few steps detected
                if i >= len(unique_steps) - 3 or len(unique_steps) <= 3:
                    filtered_steps.append(step)
            else:
                filtered_steps.append(step)
        
        # Calculate metrics
        total_expected_steps = len(self.step_patterns)
        completion_rate = (len(filtered_steps) / total_expected_steps) * 100
        
        # Calculate adherence score
        adherence_score = self._calculate_adherence_score(filtered_steps)
        
        # Get step names
        step_names = [self._get_step_name(step) for step in filtered_steps]
        
        # Find skipped steps
        all_steps = set(range(1, total_expected_steps + 1))
        detected_set = set(filtered_steps)
        skipped_steps = list(all_steps - detected_set)
        
        return ConversationFlow(
            path=filtered_steps,
            step_names=step_names,
            completion_rate=completion_rate,
            adherence_score=adherence_score,
            total_steps=total_expected_steps,
            skipped_steps=skipped_steps
        )
    
    def _get_step_name(self, step_num: int) -> str:
        """Get step name for Aditya Birla conversation flow"""
        step_names = {
            1: 'Greeting',
            2: 'Recording Notice',
            3: 'Policy Type Inquiry',
            4: 'Coverage Scope',
            5: 'Age Collection',
            6: 'Location Verification',
            7: 'Physical Activity',
            8: 'Health Assessment',
            9: 'Medical History',
            10: 'Policy Recommendation',
            11: 'Benefits Explanation',
            12: 'Pricing Discussion',
            13: 'Terms & Conditions',
            14: 'Application Process',
            15: 'Closing'
        }
        return step_names.get(step_num, f'Step {step_num}')
    
    def _calculate_adherence_score(self, detected_steps: List[int]) -> float:
        """Calculate adherence score based on step order"""
        if not detected_steps:
            return 0.0
        
        # Check if steps are in logical order
        ordered_score = 0
        for i in range(len(detected_steps) - 1):
            if detected_steps[i] < detected_steps[i + 1]:
                ordered_score += 1
        
        if len(detected_steps) > 1:
            order_ratio = ordered_score / (len(detected_steps) - 1)
        else:
            order_ratio = 1.0
        
        # Combine with completion ratio
        expected_steps = list(range(1, len(self.step_patterns) + 1))
        completion_ratio = len(detected_steps) / len(expected_steps)
        
        # Weighted average
        adherence_score = (order_ratio * 0.6 + completion_ratio * 0.4) * 100
        return min(100.0, adherence_score)
    
    def generate_step_statuses(self, conversation_flow: ConversationFlow) -> List[StepStatus]:
        """Generate detailed step status analysis"""
        statuses = []
        
        for step_num in range(1, conversation_flow.total_steps + 1):
            step_name = self._get_step_name(step_num)
            
            if step_num in conversation_flow.path:
                # Check if step is in correct order
                expected_position = step_num - 1
                actual_position = conversation_flow.path.index(step_num)
                
                if abs(actual_position - expected_position) <= 1:
                    status = 'Followed'
                else:
                    status = 'Out-of-Order'
            elif step_num in conversation_flow.skipped_steps:
                status = 'Skipped'
            else:
                status = 'Missing'
            
            statuses.append(StepStatus(
                step_number=step_num,
                step_name=step_name,
                status=status
            ))
        
        return statuses

class AdityaBirlaPolicyAnalyzer:
    """Analyzer for Aditya Birla policy recommendations and accuracy"""
    
    def __init__(self):
        self.policy_patterns = self._compile_policy_patterns()
        self.policy_rules = self._define_policy_rules()
    
    def _compile_policy_patterns(self) -> Dict[str, re.Pattern]:
        """Compile patterns to detect Aditya Birla policy mentions"""
        return {
            'activ_fit': re.compile(r'activ\s*fit', re.IGNORECASE),
            'activ_one_vital': re.compile(r'activ\s*one\s*vital', re.IGNORECASE),
            'activ_one_maxplus': re.compile(r'activ\s*one\s*maxplus', re.IGNORECASE),
            'activ_one_vytl': re.compile(r'activ\s*one\s*v[-\s]*y[-\s]*t[-\s]*l', re.IGNORECASE),
            'basic_care': re.compile(r'basic\s*care', re.IGNORECASE),
            'family_health': re.compile(r'family\s*health', re.IGNORECASE)
        }
    
    def _define_policy_rules(self) -> Dict[str, Dict]:
        """Define rules for Aditya Birla policy recommendations"""
        return {
            'Activ Fit': {
                'age_range': (18, 35),
                'health_conditions': [],
                'family_size_max': 2,
                'priority': 1,
                'features': ['Good Health Discount', 'Early Bird Discount', 'Health Returns']
            },
            'Activ One Vital': {
                'age_range': (18, 65),
                'health_conditions': ['diabetes', 'asthma', 'BP', 'chronic'],
                'priority': 2,
                'features': ['Day One Chronic Coverage', 'Chronic Management Program', 'Health Returns']
            },
            'Activ One Maxplus': {
                'age_range': (25, 60),
                'health_conditions': [],
                'priority': 3,
                'features': ['Unlimited Room Rent', 'Doubling Sum Insured', 'Health Returns']
            },
            'Activ One VYTL': {
                'age_range': (18, 65),
                'health_conditions': ['diabetes', 'chronic'],
                'priority': 2,
                'features': ['Day One Chronic Coverage', 'Chronic Management Program']
            }
        }
    
    def extract_suggested_policy(self, transcript: str) -> str:
        """Extract the policy suggested by the bot"""
        for policy_name, pattern in self.policy_patterns.items():
            if pattern.search(transcript):
                return policy_name.replace('_', ' ').title()
        
        # Look for specific policy mentions in the transcript
        policy_mentions = [
            (r'Activ One V[-\s]*Y[-\s]*T[-\s]*L', 'Activ One VYTL'),
            (r'Activ One Vital', 'Activ One Vital'),
            (r'Activ One Maxplus', 'Activ One Maxplus'),
            (r'Activ Fit', 'Activ Fit'),
            (r'Basic Care', 'Basic Care'),
            (r'Family Health', 'Family Health')
        ]
        
        for pattern, policy_name in policy_mentions:
            if re.search(pattern, transcript, re.IGNORECASE):
                return policy_name
        
        # Special handling for VYTL vs Vital confusion
        vytl_match = re.search(r'Activ One V[-\s]*Y[-\s]*T[-\s]*L', transcript, re.IGNORECASE)
        vital_match = re.search(r'Activ One Vital', transcript, re.IGNORECASE)
        
        if vytl_match and not vital_match:
            return 'Activ One VYTL'
        elif vital_match and not vytl_match:
            return 'Activ One Vital'
        elif vytl_match and vital_match:
            # If both are found, prefer the one that appears more clearly
            vytl_context = transcript[max(0, vytl_match.start()-20):vytl_match.end()+20]
            vital_context = transcript[max(0, vital_match.start()-20):vital_match.end()+20]
            
            # Check which one appears in a more definitive context
            if re.search(r'(?:recommend|suggest|policy|plan)', vytl_context, re.IGNORECASE):
                return 'Activ One VYTL'
            elif re.search(r'(?:recommend|suggest|policy|plan)', vital_context, re.IGNORECASE):
                return 'Activ One Vital'
            else:
                return 'Activ One VYTL'  # Default to VYTL if unclear
        
        # If no specific policy found, look for generic policy mentions
        policy_mention = re.search(r'(?:recommend|suggest)\s+([A-Za-z\s]+)\s+(?:policy|plan)', transcript, re.IGNORECASE)
        if policy_mention:
            return policy_mention.group(1).strip().title()
        
        return "No Policy Suggested"
    
    def determine_expected_policy(self, customer_data: CustomerData) -> str:
        """Determine the expected policy based on customer data and rules"""
        best_policy = "Activ Fit"  # Default
        best_score = 0
        
        for policy_name, rules in self.policy_rules.items():
            score = self._calculate_policy_score(customer_data, rules)
            if score > best_score:
                best_score = score
                best_policy = policy_name
        
        return best_policy
    
    def _calculate_policy_score(self, customer_data: CustomerData, rules: Dict) -> float:
        """Calculate how well customer data matches policy rules"""
        score = 0
        total_criteria = 0
        
        # Age criteria
        if 'age_range' in rules and customer_data.age:
            total_criteria += 1
            min_age, max_age = rules['age_range']
            if min_age <= customer_data.age <= max_age:
                score += 1
        
        # Health conditions criteria
        if 'health_conditions' in rules:
            total_criteria += 1
            if customer_data.health_conditions:
                # Check if customer has conditions that match policy requirements
                customer_conditions = [cond.lower() for cond in customer_data.health_conditions]
                policy_conditions = [cond.lower() for cond in rules['health_conditions']]
                
                if any(cond in customer_conditions for cond in policy_conditions):
                    score += 1
            elif not rules['health_conditions']:  # Policy for healthy people
                score += 1
        
        # Family size criteria
        if 'family_size_max' in rules:
            total_criteria += 1
            family_size = customer_data.family_size or len(customer_data.persons or [])
            if family_size <= rules['family_size_max']:
                score += 1
        
        # Priority bonus
        priority_bonus = (4 - rules.get('priority', 3)) * 0.1
        
        if total_criteria > 0:
            return (score / total_criteria) + priority_bonus
        else:
            return priority_bonus
    
    def analyze_policy_accuracy(self, customer_data: CustomerData, transcript: str) -> PolicyAnalysis:
        """Analyze policy suggestion accuracy"""
        expected_policy = self.determine_expected_policy(customer_data)
        actual_policy = self.extract_suggested_policy(transcript)
        
        # Normalize policy names for comparison
        expected_normalized = expected_policy.lower().replace(' ', '').replace('-', '')
        actual_normalized = actual_policy.lower().replace(' ', '').replace('-', '')
        
        # Check for exact match first
        match = expected_normalized == actual_normalized
        
        # If no exact match, check for similar policy names (e.g., VYTL vs Vital)
        if not match:
            # Handle common variations
            variations = {
                'activonevital': ['activonevytl', 'activonevital'],
                'activonevytl': ['activonevital', 'activonevytl'],
                'activfit': ['activfit', 'activ_fit'],
                'activonemaxplus': ['activonemaxplus', 'activonemax']
            }
            
            for base_name, variants in variations.items():
                if expected_normalized in variants and actual_normalized in variants:
                    match = True
                    break
        
        # Calculate confidence based on how clear the policy mention is
        confidence = 0.8 if actual_policy != "No Policy Suggested" else 0.3
        if match:
            confidence = min(0.95, confidence + 0.15)
        
        reasoning = f"Expected {expected_policy} based on customer profile. Bot suggested {actual_policy}."
        
        # Get policy features
        policy_features = self.policy_rules.get(expected_policy, {}).get('features', [])
        
        return PolicyAnalysis(
            expected=expected_policy,
            actual=actual_policy,
            match=match,
            confidence=confidence,
            reasoning=reasoning,
            policy_features=policy_features
        )

class AdityaBirlaTranscriptAnalysisSystem:
    """Main system for comprehensive Aditya Birla transcript analysis"""
    
    def __init__(self):
        self.transcript_parser = AdityaBirlaTranscriptParser()
        self.policy_analyzer = AdityaBirlaPolicyAnalyzer()
        self.results = []
    
    def analyze_transcript_file(self, file_path: str) -> List[CallAnalysis]:
        """Analyze all transcripts in a file"""
        transcripts = self._extract_transcripts_from_file(file_path)
        analyses = []
        
        for i, transcript_text in enumerate(transcripts):
            if not transcript_text.strip():
                continue
                
            analysis = self._analyze_single_transcript(transcript_text, f"Customer_{i+1}")
            analyses.append(analysis)
        
        self.results.extend(analyses)
        return analyses
    
    def _extract_transcripts_from_file(self, file_path: str) -> List[str]:
        """Extract individual transcripts from a file"""
        try:
            if file_path.endswith('.pdf'):
                text = self._extract_pdf_text(file_path)
            elif file_path.endswith('.docx'):
                text = self._extract_docx_text(file_path)
            else:
                with open(file_path, 'r', encoding='utf-8') as f:
                    text = f.read()
            
            # Split transcripts by "---" separator
            transcripts = re.split(r'\n\s*---\s*\n', text)
            
            # Clean and filter transcripts
            cleaned_transcripts = []
            for transcript in transcripts:
                transcript = transcript.strip()
                if len(transcript) > 100:  # Filter out very short transcripts
                    cleaned_transcripts.append(transcript)
            
            return cleaned_transcripts
            
        except Exception as e:
            print(f"Error extracting transcripts from {file_path}: {e}")
            return []
    
    def _extract_pdf_text(self, path: str) -> str:
        """Extract text from PDF file"""
        text = ""
        with open(path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            for page in pdf_reader.pages:
                text += page.extract_text()
        return text
    

        

    
    def _extract_docx_text(self, path: str) -> str:
        """Extract text from DOCX file"""
        doc = docx.Document(path)
        return '\n'.join([paragraph.text for paragraph in doc.paragraphs])
    
    def _analyze_single_transcript(self, transcript_text: str, default_name: str) -> CallAnalysis:
        """Analyze a single transcript"""
        # Extract customer data
        customer_data = self.transcript_parser.extract_customer_data(transcript_text)
        
        # Use extracted name or default
        if not customer_data.name or customer_data.name == 'Unknown':
            customer_data.name = default_name
        
        # Analyze conversation flow
        conversation_flow = self.transcript_parser.analyze_conversation_flow(transcript_text)
        
        # Generate step statuses
        step_statuses = self.transcript_parser.generate_step_statuses(conversation_flow)
        
        # Analyze policy accuracy
        policy_analysis = self.policy_analyzer.analyze_policy_accuracy(customer_data, transcript_text)
        
        # Determine outcome
        outcome = self._determine_outcome(conversation_flow, policy_analysis)
        
        # Determine call quality
        call_quality = self._assess_call_quality(transcript_text)
        
        return CallAnalysis(
            customer_name=customer_data.name,
            collected_entities=customer_data,
            policy_analysis=policy_analysis,
            conversation_flow=conversation_flow,
            step_statuses=step_statuses,
            outcome=outcome,
            transcript_text=transcript_text,
            call_quality=call_quality,
            language_mix=customer_data.language
        )
    
    def _determine_outcome(self, flow: ConversationFlow, policy: PolicyAnalysis) -> str:
        """Determine overall call outcome"""
        if flow.completion_rate >= 80 and policy.match:
            return "Success"
        elif flow.completion_rate >= 60 or policy.confidence >= 0.7:
            return "Partial"
        else:
            return "Failed"
    
    def _assess_call_quality(self, transcript: str) -> str:
        """Assess the quality of the call based on transcript"""
        # Count interruptions and issues
        issues = 0
        
        # Check for silence markers
        silence_count = len(re.findall(r'<silence>', transcript, re.IGNORECASE))
        issues += silence_count
        
        # Check for repeated phrases
        repeated_phrases = len(re.findall(r'(?:Hello\?|Are you there\?)', transcript, re.IGNORECASE))
        issues += repeated_phrases
        
        # Check for unclear responses
        unclear_responses = len(re.findall(r'(?:मुझे अच्छे से समझ नहीं आया|I didn\'t understand)', transcript, re.IGNORECASE))
        issues += unclear_responses
        
        if issues == 0:
            return "Excellent"
        elif issues <= 2:
            return "Good"
        else:
            return "Poor"
    
    def generate_comprehensive_report(self) -> Dict[str, Any]:
        """Generate comprehensive analysis report"""
        if not self.results:
            return {"error": "No analysis results available"}
        
        # Calculate aggregate metrics
        total_calls = len(self.results)
        successful_calls = len([r for r in self.results if r.outcome == "Success"])
        success_rate = (successful_calls / total_calls) * 100
        
        # Calculate average adherence
        adherence_scores = [r.conversation_flow.adherence_score for r in self.results]
        avg_adherence = sum(adherence_scores) / len(adherence_scores) if adherence_scores else 0
        
        # Calculate step completion rates
        step_completion_rates = {}
        for step_num in range(1, 16):  # Aditya Birla has 15 steps
            completed = len([r for r in self.results 
                           if step_num in r.conversation_flow.path])
            step_completion_rates[step_num] = (completed / total_calls) * 100
        
        # Find common paths
        path_counter = Counter()
        for result in self.results:
            path_str = "→".join(map(str, result.conversation_flow.path))
            path_counter[path_str] += 1
        
        common_paths = [path for path, count in path_counter.most_common(5)]
        
        # Calculate policy accuracy
        policy_matches = len([r for r in self.results if r.policy_analysis.match])
        policy_accuracy = (policy_matches / total_calls) * 100
        
        # Language analysis
        language_stats = Counter([r.language_mix for r in self.results])
        
        # Call quality analysis
        quality_stats = Counter([r.call_quality for r in self.results])
        
        return {
            "summary": {
                "total_calls": total_calls,
                "success_rate": round(success_rate, 1),
                "average_adherence": round(avg_adherence, 1),
                "policy_accuracy": round(policy_accuracy, 1)
            },
            "step_analysis": {
                "completion_rates": step_completion_rates,
                "step_names": {num: self.transcript_parser._get_step_name(num) 
                              for num in range(1, 16)}
            },
            "flow_analysis": {
                "common_paths": common_paths,
                "path_distribution": dict(path_counter)
            },
            "policy_analysis": {
                "matches": policy_matches,
                "mismatches": total_calls - policy_matches,
                "accuracy_by_customer": [
                    {
                        "customer": r.customer_name,
                        "expected": r.policy_analysis.expected,
                        "actual": r.policy_analysis.actual,
                        "match": r.policy_analysis.match,
                        "confidence": round(r.policy_analysis.confidence, 2)
                    }
                    for r in self.results
                ]
            },
            "language_analysis": dict(language_stats),
            "quality_analysis": dict(quality_stats),
            "individual_results": [
                {
                    "customer_name": r.customer_name,
                    "collected_entities": r.collected_entities.to_dict(),
                    "conversation_path": r.conversation_flow.path,
                    "step_names": r.conversation_flow.step_names,
                    "completion_rate": round(r.conversation_flow.completion_rate, 1),
                    "adherence_score": round(r.conversation_flow.adherence_score, 1),
                    "policy_expected": r.policy_analysis.expected,
                    "policy_actual": r.policy_analysis.actual,
                    "policy_match": r.policy_analysis.match,
                    "outcome": r.outcome,
                    "call_quality": r.call_quality,
                    "language": r.language_mix,
                    "step_statuses": [
                        {
                            "step": s.step_number,
                            "name": s.step_name,
                            "status": s.status
                        }
                        for s in r.step_statuses
                    ]
                }
                for r in self.results
            ]
        }

# Example usage and main execution
def main():
    """Main execution function"""
    # Initialize the analysis system
    transcript_file_path = "sample_transcript.txt"  # Update with actual path
    
    # Create analyzer
    analyzer = AdityaBirlaTranscriptAnalysisSystem()
    
    # Analyze transcripts
    print("Analyzing Aditya Birla transcripts...")
    results = analyzer.analyze_transcript_file(transcript_file_path)
    
    print(f"Analyzed {len(results)} calls")
    
    # Generate comprehensive report
    print("Generating comprehensive report...")
    report = analyzer.generate_comprehensive_report()
    
    # Print summary
    print("\n" + "="*50)
    print("ADITYA BIRLA ANALYSIS SUMMARY")
    print("="*50)
    print(f"Total Calls: {report['summary']['total_calls']}")
    print(f"Success Rate: {report['summary']['success_rate']}%")
    print(f"Average Adherence: {report['summary']['average_adherence']}%")
    print(f"Policy Accuracy: {report['summary']['policy_accuracy']}%")
    
    print("\nLanguage Distribution:")
    for lang, count in report['language_analysis'].items():
        print(f"  {lang}: {count} calls")
    
    print("\nCall Quality Distribution:")
    for quality, count in report['quality_analysis'].items():
        print(f"  {quality}: {count} calls")
    
    print("\nAnalysis complete!")

if __name__ == "__main__":
    main()