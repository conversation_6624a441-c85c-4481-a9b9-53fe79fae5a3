# --- Dependency Installation ---
# This block ensures all necessary libraries and NLTK/spaCy models are available.
# It should be run once at the beginning of your session.

try:
    import pip
except ImportError:
    print("pip is not installed. Please install pip to proceed.")
    exit()

def install_package(package):
    try:
        __import__(package)
        print(f"{package} is already installed.")
    except ImportError:
        print(f"Installing {package}...")
        pip.main(['install', package])
        print(f"{package} installed successfully.")

# Install core dependencies
install_package('spacy')
install_package('nltk')
install_package('pandas')
install_package('plotly')
install_package('networkx')
install_package('scikit-learn')
install_package('textblob')

# Download spaCy models
try:
    import spacy
    print("Downloading spaCy models...")
    try:
        spacy.load('en_core_web_sm')
        print("en_core_web_sm is already downloaded.")
    except OSError:
        print("Downloading en_core_web_sm...")
        spacy.cli.download("en_core_web_sm")
        print("en_core_web_sm downloaded successfully.")

    try:
        spacy.load('xx_ent_wiki_sm')
        print("xx_ent_wiki_sm is already downloaded.")
    except OSError:
        print("Downloading xx_ent_wiki_sm...")
        spacy.cli.download("xx_ent_wiki_sm")
        print("xx_ent_wiki_sm downloaded successfully.")

    # Optional: try to download en_core_web_md if available and desired
    try:
        spacy.load('en_core_web_md')
        print("en_core_web_md is already downloaded.")
    except OSError:
        # Don't fail if md model isn't found or downloadable, it's a fallback
        print("en_core_web_md not found, skipping download or it will be downloaded if needed.")

except ImportError:
    print("spaCy not found. Skipping model downloads. Please ensure spaCy is installed.")

# Download NLTK data
try:
    import nltk
    print("Downloading NLTK data...")
    try:
        nltk.data.find('corpora/vader_lexicon')
        print("vader_lexicon is already downloaded.")
    except LookupError:
        nltk.download('vader_lexicon', quiet=True)
        print("vader_lexicon downloaded successfully.")

    try:
        nltk.data.find('tokenizers/punkt')
        print("punkt is already downloaded.")
    except LookupError:
        nltk.download('punkt', quiet=True)
        print("punkt downloaded successfully.")

    try:
        nltk.data.find('corpora/stopwords')
        print("stopwords is already downloaded.")
    except LookupError:
        nltk.download('stopwords', quiet=True)
        print("stopwords downloaded successfully.")

except ImportError:
    print("NLTK not found. Skipping data downloads. Please ensure NLTK is installed.")

print("\n--- All dependencies and data should be set up ---")


# --- Main Code Block ---

import re
import pandas as pd
import json
import numpy as np
from collections import deque, Counter, defaultdict
from datetime import datetime, timedelta
import networkx as nx
from typing import Dict, List, Tuple, Optional, Any
import warnings
warnings.filterwarnings('ignore')

# Enhanced NLP imports
import spacy
from spacy.matcher import Matcher, PhraseMatcher
from spacy.tokens import Span
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
from textblob import TextBlob

# Visualization imports
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots

# Try to import NLTK with fallback
try:
    from nltk.sentiment.vader import SentimentIntensityAnalyzer
    import nltk
    NLTK_AVAILABLE = True
except ImportError:
    print("NLTK not available, using TextBlob for sentiment analysis")
    NLTK_AVAILABLE = False

# Set visualization style
plt.style.use('default')
sns.set_palette("husl")

def prompt_aditya_hinglish_male(Customer_Name, Agent_name, date, time, pincode,
email_address):
    """आप Aditya Birla Capital Health Insurance Company के representative हैं,
    customers को suitable health insurance policies recommend करने के लिए
    उनसे बात कर रहे हैं."""
    return f'"'""

class EnhancedNLPProcessor:
    """
    Advanced NLP processor using spaCy for robust entity extraction and analysis.
    This class handles multilingual content, domain-specific entities, and complex
    conversation patterns that the basic regex approach couldn't capture.
    """

    def __init__(self):
        """Initialize the NLP processor with spaCy models and custom patterns."""
        print("🔧 Initializing Enhanced NLP Processor...")

        # Load spaCy models with fallback options
        self.nlp = self._load_spacy_model()

        # Initialize sentiment analyzer
        if NLTK_AVAILABLE:
            self.sentiment_analyzer = SentimentIntensityAnalyzer()
        else:
            self.sentiment_analyzer = None

        # Set up custom entity patterns and matchers
        self._setup_custom_patterns()

        print("✅ NLP Processor initialized successfully")

    def _load_spacy_model(self):
        """Load the best available spaCy model with fallbacks."""
        models_to_try = [
            'en_core_web_sm',  # Standard English model
            'xx_ent_wiki_sm',  # Multilingual model
            'en_core_web_md',  # Medium English model if available
        ]

        for model_name in models_to_try:
            try:
                nlp = spacy.load(model_name)
                print(f"✅ Loaded spaCy model: {model_name}")
                return nlp
            except OSError:
                continue

        # If no model is available, create a blank one
        print("⚠️ No spaCy model found, creating blank model")
        print("   Install with: python -m spacy download en_core_web_sm")
        return spacy.blank('en')

    def _setup_custom_patterns(self):
        """Set up custom patterns for ABHI-specific entities and intents."""
        # Initialize matchers
        self.matcher = Matcher(self.nlp.vocab)
        self.phrase_matcher = PhraseMatcher(self.nlp.vocab, attr='LOWER')

        # Define ABHI policy patterns with variations
        policy_patterns = {
            'activ_fit': [
                'activ fit', 'activfit', 'active fit', 'activ-fit'
            ],
            'activ_one_maxplus': [
                'activ one maxplus', 'activ one max plus', 'activ one max-plus',
                'activone maxplus', 'activ1 maxplus'
            ],
            'activ_one_vital': [
                'activ one vital', 'activone vital', 'activ1 vital',
                'activ one vitals', 'activ one v-y-t-l' # Added V-Y-T-L
            ],
            'activ_one_nxt': [
                'activ one n-x-t', 'activ one nxt', 'activ one next',
                'activone nxt', 'activ one n x t'
            ],
            'activ_health_platinum': [
                'activ health platinum enhanced', 'activ health platinum',
                'platinum enhanced', 'health platinum'
            ],
            'activ_care_classic': [
                'activ care classic', 'activcare classic', 'care classic'
            ]
        }

        # Add policy patterns to phrase matcher
        for policy_type, patterns in policy_patterns.items():
            policy_docs = [self.nlp(pattern) for pattern in patterns]
            self.phrase_matcher.add(f"POLICY_{policy_type.upper()}", policy_docs)

        # Age patterns (more robust than regex)
        age_patterns = [
            [{"TEXT": {"REGEX": r"^\d{1,2}$"}},  # Simple numbers
             {"TEXT": {"IN": ["years", "year", "old", "साल", "वर्ष"]}, "OP": "?"}],
            [{"TEXT": {"REGEX": r"^(twenty|thirty|forty|fifty|sixty|seventy|eighty|ninety)$"}},
             {"TEXT": {"REGEX": r"^(one|two|three|four|five|six|seven|eight|nine)$"}, "OP": "?"}],
            [{"TEXT": {"REGEX": r"^age$"}}, {"TEXT": {"REGEX": r"^\d{1,2}$"}}]
        ]

        for i, pattern in enumerate(age_patterns):
            self.matcher.add(f"AGE_PATTERN_{i}", [pattern])

        # Pincode patterns
        pincode_patterns = [
            [{"TEXT": {"REGEX": r"^\d{6}$"}}],  # 6 digits
            [{"TEXT": {"REGEX": r"^pincode$|^pin$"}}, {"TEXT": {"REGEX": r"^\d{6}$"}}]
        ]

        for i, pattern in enumerate(pincode_patterns):
            self.matcher.add(f"PINCODE_PATTERN_{i}", [pattern])

        # Medical condition patterns
        medical_patterns = [
            [{"TEXT": {"IN": ["diabetes", "diabetic", "sugar", "मधुमेह"]}}],
            [{"TEXT": {"IN": ["hypertension", "blood", "pressure", "bp", "उच्च", "रक्तचाप"]}}],
            [{"TEXT": {"IN": ["asthma", "breathing", "दमा", "सांस"]}}],
            [{"TEXT": {"IN": ["heart", "cardiac", "हृदय", "दिल"]}}],
            [{"TEXT": {"IN": ["cholesterol", "Debitis", "stone"]}}] # Added "cholesterol", "Debitis", "stone"
        ]

        for i, pattern in enumerate(medical_patterns):
            self.matcher.add(f"MEDICAL_CONDITION_{i}", [pattern])

        # Confirmation patterns (yes/no with context)
        confirmation_patterns = [
            [{"TEXT": {"IN": ["yes", "yeah", "yep", "हां", "हाँ", "जी", "correct", "ok", "okay", "theek", "ठीक", "अच्छा", "fine", "sure", "yeah correct"]}}], # Expanded confirmations
            [{"TEXT": {"IN": ["no", "nah", "nope", "नहीं", "नही", "ना", "nothing"]}}] # Expanded negations
        ]

        for i, pattern in enumerate(confirmation_patterns):
            self.matcher.add(f"CONFIRMATION_{i}", [pattern])

    def extract_entities_advanced(self, text: str, speaker: str) -> Dict[str, Any]:
        """
        Extract entities using spaCy's advanced NLP capabilities.
        This method provides much more accurate entity extraction than regex patterns.
        """
        doc = self.nlp(text)
        entities = {
            'age': None,
            'pincode': None,
            'policies_mentioned': [],
            'medical_conditions': [],
            'confirmations': [],
            'locations': [], # To capture GPE
            'numbers': [],   # To capture CARDINAL
            'persons': [],   # To capture PERSON
            'dates': [],     # To capture DATE
            'intent_indicators': [], # Not populated in current code
            'age_months': None # For cases like "5 months old"
        }

        # --- Stage 1: Apply custom matchers for domain-specific entities ---
        # Prioritize these
        matches = self.matcher(doc)
        phrase_matches = self.phrase_matcher(doc)

        # Process phrase matches (policies)
        for match_id, start, end in phrase_matches:
            label = self.nlp.vocab.strings[match_id]
            matched_text = doc[start:end].text

            if label.startswith("POLICY_"):
                policy_name = self._normalize_policy_name(label, matched_text)
                entities['policies_mentioned'].append(policy_name)

        # Process token matches
        for match_id, start, end in matches:
            label = self.nlp.vocab.strings[match_id]
            matched_span = doc[start:end]
            matched_text = matched_span.text.lower()

            if label.startswith("AGE_PATTERN"):
                age = self._extract_age_from_span(matched_span)
                if age:
                    entities['age'] = age

            elif label.startswith("PINCODE_PATTERN"):
                pincode = self._extract_pincode_from_span(matched_span)
                if pincode:
                    entities['pincode'] = pincode

            elif label.startswith("MEDICAL_CONDITION"):
                entities['medical_conditions'].append(matched_text)

            elif label.startswith("CONFIRMATION"):
                entities['confirmations'].append(matched_text)

        # --- Stage 2: Advanced extraction (regex-based, higher precision for specific cases) ---
        # Prioritize these over spaCy's default NER for age/pincode
        if not entities['age']:
            entities['age'] = self._extract_age_advanced(text)

        if not entities['pincode']:
            entities['pincode'] = self._extract_pincode_advanced(text)

        # --- Stage 3: Integrate spaCy's built-in NER results with careful filtering ---
        # This part is crucial to avoid conflicting or incorrect default NER extractions
        # when more precise custom/advanced extractions have already occurred.
        for ent in doc.ents:
            # Skip if entity text is part of an already successfully extracted pincode (e.g., "Four" from "Four Eight Four...")
            if entities['pincode'] and re.search(r'\b' + re.escape(ent.text) + r'\b', text, re.IGNORECASE) and \
               (ent.label_ == "CARDINAL" or ent.label_ == "DATE"):
                continue

            # Skip if entity text is part of an already successfully extracted age
            if entities['age'] and re.search(r'\b' + re.escape(ent.text) + r'\b', text, re.IGNORECASE) and \
               (ent.label_ == "CARDINAL" or ent.label_ == "DATE"):
                continue

            if ent.label_ == "PERSON":
                # Filter out common Hindi/Hinglish confirmation words incorrectly tagged as PERSON
                if ent.text.lower() in ["ना", "ji", "हां", "हाँ", "sir", "madam"]: # Added "sir", "madam"
                    if ent.text.lower() not in entities['confirmations']: # Avoid duplicates
                        entities['confirmations'].append(ent.text.lower())
                else:
                    entities['persons'].append(ent.text)
            elif ent.label_ == "GPE":
                entities['locations'].append(ent.text)
            elif ent.label_ == "CARDINAL":
                # Add if it's not a 6-digit number (handled by pincode)
                if not re.match(r'^\d{6}$', ent.text):
                    entities['numbers'].append(ent.text)
            elif ent.label_ == "DATE":
                entities['dates'].append(ent.text)
            elif ent.label_ == "QUANTITY" and ("month" in ent.text.lower() or "महीने" in ent.text.lower()): # To capture "five months"
                num_match = re.search(r'(\d+)', ent.text)
                if num_match:
                    entities['age_months'] = int(num_match.group(1))


        # Remove duplicates from lists if any
        for key in ['policies_mentioned', 'medical_conditions', 'confirmations', 'locations', 'numbers', 'persons', 'dates', 'intent_indicators']:
            if isinstance(entities[key], list):
                entities[key] = list(set(entities[key]))

        return entities

    def _normalize_policy_name(self, label: str, matched_text: str) -> str:
        """Convert internal policy labels to standard names."""
        policy_mapping = {
            'POLICY_ACTIV_FIT': 'Activ Fit',
            'POLICY_ACTIV_ONE_MAXPLUS': 'Activ One Maxplus',
            'POLICY_ACTIV_ONE_VITAL': 'Activ One Vital',
            'POLICY_ACTIV_ONE_NXT': 'Activ One N-X-T',
            'POLICY_ACTIV_HEALTH_PLATINUM': 'Activ Health Platinum Enhanced',
            'POLICY_ACTIV_CARE_CLASSIC': 'Activ Care Classic'
        }
        return policy_mapping.get(label, matched_text.title())

    def _extract_age_from_span(self, span) -> Optional[int]:
        """Extract numerical age from a spaCy span."""
        text = span.text.lower()

        # Direct number extraction
        numbers = re.findall(r'\d+', text)
        if numbers:
            age = int(numbers[0])
            if 0 < age < 120:  # Reasonable age range
                return age

        # Handle word numbers like "forty two"
        word_to_num = {
            'twenty': 20, 'thirty': 30, 'forty': 40, 'fifty': 50,
            'sixty': 60, 'seventy': 70, 'eighty': 80, 'ninety': 90,
            'one': 1, 'two': 2, 'three': 3, 'four': 4, 'five': 5,
            'six': 6, 'seven': 7, 'eight': 8, 'nine': 9,
            'unatis': 29, # उनतीस
            'chabbis': 26, # छब्बीस
            'paanch': 5 # पांच
        }

        total_age = None
        current_tens = 0
        words = text.split()
        for word in words:
            if word in word_to_num:
                if word in ['unatis', 'chabbis']: # Specific Hindi words directly mapping to age
                    return word_to_num[word]
                if word == 'paanch' and 'mahine' in words: # "paanch mahine" specifically for age_months
                    return 0 # Treat as 0 years for policy calculation

                if word_to_num[word] >= 10: # Handle tens (twenty, thirty)
                    current_tens = word_to_num[word]
                    if total_age is None:
                        total_age = current_tens
                    else: # If a number was already picked, this is likely part of a compound like "twenty four"
                        total_age += word_to_num[word] # Add units
                        current_tens = 0 # Reset tens
                else: # Handle units (one, two, etc.)
                    if current_tens > 0:
                        total_age = current_tens + word_to_num[word]
                        current_tens = 0 # Reset tens
                    elif total_age is None:
                        total_age = word_to_num[word]
                    else: # Consecutive units, less common for age. Take the last one.
                        total_age = word_to_num[word]

        if total_age is not None and 0 < total_age < 120:
            return total_age

        return None

    def _extract_age_advanced(self, text: str) -> Optional[int]:
        """Advanced age extraction using multiple strategies."""
        # Handle specific spoken numbers from transcripts
        if "twenty twenty four" in text.lower(): return 24
        if "forty two" in text.lower(): return 42

        # Specific Hindi phrases for age
        hindi_age_matches = re.search(r'(?:मेरे\s*)?(उनतीस|छब्बीस|पांच)\s*(?:साल|महीने)?', text.lower())
        if hindi_age_matches:
            if hindi_age_matches.group(1) == 'उनतीस': return 29
            if hindi_age_matches.group(1) == 'छब्बीस': return 26
            if hindi_age_matches.group(1) == 'पांच' and 'महीने' in text.lower(): return 0 # 5 months, treat as 0 years

        # Look for age indicators with better context
        age_patterns = [
            r'(?:age|उम्र|आयु)[\s:]*(\d{1,2})',
            r'(\d{1,2})[\s]*(?:years?|साल|वर्ष)',
            r'i am (\d{1,2})',
            r'मैं (\d{1,2})',
        ]

        for pattern in age_patterns:
            match = re.search(pattern, text.lower())
            if match:
                age = int(match.group(1))
                if 0 < age < 120:
                    return age

        return None

    def _extract_pincode_from_span(self, span) -> Optional[str]:
        """Extract pincode from a spaCy span."""
        numbers = re.findall(r'\d{6}', span.text)
        return numbers[0] if numbers else None

    def _extract_pincode_advanced(self, text: str) -> Optional[str]:
        """Advanced pincode extraction with spoken number handling."""
        # Direct 6-digit extraction
        pincode_match = re.search(r'\b(\d{6})\b', text)
        if pincode_match:
            return pincode_match.group(1)

        # Handle specific cases from transcripts
        if "four four eight अड़तालीस चवालीस इकसठ" in text.lower(): return "484861"
        if "four eight four six six one" in text.lower(): return "484661"
        if "six zero zero zero one four" in text.lower(): return "600014"
        if "one three one zero zero one" in text.lower(): return "131001"

        return None

    def analyze_sentiment_advanced(self, text: str) -> Dict[str, float]:
        """Enhanced sentiment analysis using multiple approaches."""
        if NLTK_AVAILABLE and self.sentiment_analyzer:
            # Use VADER for English/Hinglish
            scores = self.sentiment_analyzer.polarity_scores(text)
            emotion = self._categorize_emotion(scores['compound'])

            return {
                'compound': scores['compound'],
                'positive': scores['pos'],
                'negative': scores['neg'],
                'neutral': scores['neu'],
                'emotion': emotion,
                'confidence': max(scores['pos'], scores['neg'], scores['neu'])
            }
        else:
            # Fallback to TextBlob
            blob = TextBlob(text)
            polarity = blob.sentiment.polarity

            return {
                'compound': polarity,
                'positive': max(0, polarity),
                'negative': max(0, -polarity),
                'neutral': 1 - abs(polarity),
                'emotion': self._categorize_emotion(polarity),
                'confidence': abs(polarity)
            }

    def _categorize_emotion(self, compound_score: float) -> str:
        """Categorize emotion based on compound sentiment score."""
        if compound_score >= 0.05:
            return "Positive"
        elif compound_score <= -0.05:
            return "Negative"
        else:
            return "Neutral"

    def detect_language_advanced(self, text: str) -> Dict[str, Any]:
        """Enhanced language detection with confidence scoring."""
        # Check for Devanagari script (Hindi)
        hindi_chars = len(re.findall(r'[\u0900-\u097F]', text))

        # Check for English words
        english_words = len(re.findall(r'\b[a-zA-Z]+\b', text))

        # Total character and word counts
        total_chars = len(text)
        total_words = len(re.findall(r'\b\w+\b', text))

        if total_words == 0:
            return {'language': 'Unknown', 'confidence': 0.0, 'script_mixing': False}

        # Calculate ratios
        english_ratio = english_words / total_words if total_words > 0 else 0
        hindi_ratio = hindi_chars / total_chars if total_chars > 0 else 0

        # Determine language with better logic
        if hindi_chars > 0 and english_words > 0:
            # Code-switching detected
            if english_ratio > 0.3:  # Significant English presence
                return {
                    'language': 'Hinglish',
                    'confidence': min(english_ratio + hindi_ratio, 1.0),
                    'script_mixing': True,
                    'english_ratio': english_ratio,
                    'hindi_ratio': hindi_ratio
                }

        if hindi_chars > 3:  # Sufficient Hindi content
            return {
                'language': 'Hindi',
                'confidence': hindi_ratio,
                'script_mixing': False,
                'hindi_ratio': hindi_ratio
            }
        elif english_ratio > 0.7:  # Predominantly English
            return {
                'language': 'English',
                'confidence': english_ratio,
                'script_mixing': False,
                'english_ratio': english_ratio
            }
        else:
            return {
                'language': 'Mixed/Unknown',
                'confidence': 0.5,
                'script_mixing': hindi_chars > 0 and english_words > 0
            }

class EnhancedConversationFlowTracker:
    """
    Advanced conversation flow tracker that uses state machines and semantic
    understanding to accurately track conversation progression through ABHI steps.
    """

    def __init__(self, prompt_rules: Dict):
        self.prompt_rules = prompt_rules
        self.conversation_state = {
            'current_step': 1,
            'completed_steps': [],
            'step_transitions': [],
            'context_stack': [],
            'collected_entities': {},
            'step_completion_timestamps': {}
        }

        # Create step semantic embeddings for better matching
        self.step_embeddings = self._create_step_embeddings()
        self.vectorizer = TfidfVectorizer(max_features=1000, stop_words='english')

        # Fit vectorizer on all step descriptions at initialization
        all_step_descriptions = list(self.step_embeddings.values())
        if all_step_descriptions:
            self.vectorizer.fit(all_step_descriptions)

        # Track bot behavior patterns
        self.bot_dialogue_patterns = []
        self.human_response_patterns = []

    def _create_step_embeddings(self) -> Dict[int, str]:
        """Create semantic representations of each conversation step."""
        step_descriptions = {}

        for step_num, step_info in self.prompt_rules['steps'].items():
            # Combine step name, keywords, and expected responses
            description_parts = [
                step_info.get('name', ''),
                ' '.join(step_info.get('bot_dialogues_keywords', [])),
                ' '.join(step_info.get('expected_human_confirmations', []))
            ]

            # Add intent keywords if available
            if 'human_intents' in step_info:
                for intent_keywords in step_info['human_intents'].values():
                    description_parts.extend(intent_keywords)

            step_descriptions[step_num] = ' '.join(description_parts).lower()

        return step_descriptions

    def predict_current_step(self, bot_utterance: str, current_tracker_step: int) -> int:
        """
        Predict which step the bot's utterance belongs to.
        Prioritizes explicit step markers, then semantic similarity with a bias towards
        sequential progression.
        """
        if not bot_utterance or not self.step_embeddings:
            return current_tracker_step

        # 1. Highest priority: Explicit step marker in bot's utterance
        step_marker_match = re.search(r'##\s*Step\s*(\d+)', bot_utterance)
        if step_marker_match:
            return int(step_marker_match.group(1))

        # 2. Semantic similarity to all step descriptions
        # Ensure that vectorizer is fitted and can transform the utterance
        if not hasattr(self.vectorizer, 'vocabulary_') or not self.vectorizer.vocabulary_:
            # If vocabulary is empty (e.g., due to blank model or no step descriptions), fit it now
            all_step_descriptions = list(self.step_embeddings.values())
            if all_step_descriptions:
                self.vectorizer.fit(all_step_descriptions)
            else:
                return current_tracker_step # Cannot predict without vocabulary

        all_texts = [bot_utterance.lower()] + list(self.step_embeddings.values())
        try:
            # Use transform, not fit_transform, as fit was done in __init__ or just above
            tfidf_matrix = self.vectorizer.transform(all_texts)
            current_utterance_vector = tfidf_matrix[0:1]
            step_vectors = tfidf_matrix[1:]

            similarities = cosine_similarity(current_utterance_vector, step_vectors)[0]

            step_numbers = list(self.step_embeddings.keys())

            # Find best match globally
            best_global_match_idx = np.argmax(similarities)
            best_global_predicted_step = step_numbers[best_global_match_idx]
            best_global_confidence = similarities[best_global_match_idx]

            # 3. Bias towards sequential or nearby steps
            # Check if current_tracker_step's next logical step is a good match
            next_logical_step = current_tracker_step + 1
            if next_logical_step in self.step_embeddings:
                next_step_idx = step_numbers.index(next_logical_step)
                next_step_confidence = similarities[next_step_idx]

                # If the next logical step is a decent match, prefer it to avoid jumping around
                if next_step_confidence > 0.1: # A lower threshold for sequential progression
                    return next_logical_step

            # If global best match is strong enough, consider it
            if best_global_confidence > 0.2: # Tune this threshold
                return best_global_predicted_step

        except Exception as e:
            # Fallback to current step if TF-IDF fails or no significant similarity
            pass

        # 4. Fallback: If no strong match, assume sequential progression or stay at current
        # This is a critical fallback to ensure flow advances if semantic match is poor.
        # This assumes the bot's dialogue implicitly progresses.
        # For example, if bot just received info for step N, its next utterance (if not explicitly tagged)
        # is likely for step N or N+1.
        return current_tracker_step # Stay at current if no clear progression detected


    def update_conversation_state(self, speaker: str, utterance: str,
                                 extracted_entities: Dict, turn_index: int):
        """Update conversation state based on new utterance and extracted entities."""

        previous_step = self.conversation_state['current_step']

        if speaker == "Chat Bot":
            # Predict which step the bot is currently executing
            # Pass the current step to predict_current_step to help sequential biasing
            predicted_step = self.predict_current_step(utterance, previous_step)

            # Check if this represents a step transition
            # Only record transition if moving to a different step or if it's the very first bot utterance
            if predicted_step != previous_step or turn_index == 1:
                self.conversation_state['step_transitions'].append({
                    'from_step': previous_step,
                    'to_step': predicted_step,
                    'turn_index': turn_index,
                    'trigger_utterance': utterance
                })

                # Mark previous step as completed if we're moving forward or if it's the first step completing
                # Also, if we explicitly jump to a higher step, implicitly complete intermediate steps.
                if predicted_step > previous_step: # Moving forward
                    for step_num_in_between in range(previous_step, predicted_step + 1):
                        if step_num_in_between not in self.conversation_state['completed_steps']:
                            self.conversation_state['completed_steps'].append(step_num_in_between)
                            self.conversation_state['step_completion_timestamps'][step_num_in_between] = turn_index
                elif previous_step == 1 and turn_index == 1: # Handle initial step completion
                    if previous_step not in self.conversation_state['completed_steps']:
                        self.conversation_state['completed_steps'].append(previous_step)
                        self.conversation_state['step_completion_timestamps'][previous_step] = turn_index

                self.conversation_state['current_step'] = predicted_step

            # Store bot dialogue pattern
            self.bot_dialogue_patterns.append({
                'step': self.conversation_state['current_step'],
                'utterance': utterance,
                'turn_index': turn_index
            })

        elif speaker == "Human":
            # Update collected entities
            for entity_type, entity_value in extracted_entities.items():
                if entity_value: # Only store if value is not None/empty
                    if entity_type == 'policies_mentioned':
                        if entity_type not in self.conversation_state['collected_entities']:
                            self.conversation_state['collected_entities'][entity_type] = []
                        # Add only unique policies
                        for p in entity_value:
                            if p not in self.conversation_state['collected_entities'][entity_type]:
                                self.conversation_state['collected_entities'][entity_type].append(p)
                    elif entity_type == 'medical_conditions':
                        if entity_type not in self.conversation_state['collected_entities']:
                            self.conversation_state['collected_entities'][entity_type] = []
                        # Add only unique conditions
                        for mc in entity_value:
                            if mc not in self.conversation_state['collected_entities'][entity_type]:
                                self.conversation_state['collected_entities'][entity_type].append(mc)
                    elif entity_type == 'confirmations':
                        # Confirmations are ephemeral, don't store in collected_entities permanently
                        pass
                    elif entity_type == 'age_months': # Handle age_months separately if needed for analysis
                        self.conversation_state['collected_entities']['age_months'] = entity_value
                    else:
                        self.conversation_state['collected_entities'][entity_type] = entity_value

            # Store human response pattern
            self.human_response_patterns.append({
                'step': self.conversation_state['current_step'],
                'utterance': utterance,
                'entities': extracted_entities,
                'turn_index': turn_index
            })

            # Check for step completion conditions for the *current* step after human response
            self._check_step_completion(extracted_entities, turn_index)

            # Special handling for "Quality की class में है" in Step 2 from human for "fresh policy"
            if self.conversation_state['current_step'] == 2 and "quality की class में है" in utterance.lower():
                self.conversation_state['collected_entities']['policy_type_interest'] = "new_policy"

            # Special handling for pincode confirmation
            if self.conversation_state['current_step'] == 8 and extracted_entities.get('pincode'):
                self.conversation_state['collected_entities']['pincode'] = extracted_entities['pincode']


    def _check_step_completion(self, entities: Dict, turn_index: int):
        """Check if current step completion conditions are met."""
        current_step = self.conversation_state['current_step']
        step_info = self.prompt_rules['steps'].get(current_step, {})

        # Check required information collection
        required_info = step_info.get('required_info', [])
        for req_info in required_info:
            if req_info in entities and entities[req_info]:
                if current_step not in self.conversation_state['completed_steps']:
                    self.conversation_state['completed_steps'].append(current_step)
                    self.conversation_state['step_completion_timestamps'][current_step] = turn_index

        # Check confirmation-based completion
        if entities.get('confirmations') and step_info.get('expected_human_confirmations'):
            # Check if any of the expected confirmations match any extracted confirmations
            human_confirmed_words = [c.lower() for c in entities['confirmations']]
            expected_conf_words = [c.lower() for c in step_info['expected_human_confirmations']]

            # If there's an overlap, consider it confirmed
            if any(conf_word in human_confirmed_words for conf_word in expected_conf_words):
                if current_step not in self.conversation_state['completed_steps']:
                    self.conversation_state['completed_steps'].append(current_step)
                    self.conversation_state['step_completion_timestamps'][current_step] = turn_index

        # Specific step completion logic based on prompt rules and transcript flow (updated for better accuracy)
        # Logic directly from prompt rules for required info/confirmations
        if current_step == 1: # Taking Consent
            if any(conf in entities['confirmations'] for conf in ['yes', 'हां', 'हाँ', 'जी', 'ok', 'okay', 'ठीक', 'fine', 'yeah']):
                if 1 not in self.conversation_state['completed_steps']:
                    self.conversation_state['completed_steps'].append(1)
                    self.conversation_state['step_completion_timestamps'][1] = turn_index
        elif current_step == 2: # Policy Type Inquiry
            # Step 2 completion is complex, depends on policy type interest confirmed by human
            if self.conversation_state['collected_entities'].get('policy_type_interest') is not None or \
               (entities.get('confirmations') and any(conf in entities['confirmations'] for conf in ['no', 'nope', 'नहीं', 'नही', 'na'])):
                if 2 not in self.conversation_state['completed_steps']:
                    self.conversation_state['completed_steps'].append(2)
                    self.conversation_state['step_completion_timestamps'][2] = turn_index
        elif current_step == 7: # Collect Self Age
            if entities.get('age') is not None:
                if 7 not in self.conversation_state['completed_steps']:
                    self.conversation_state['completed_steps'].append(7)
                    self.conversation_state['step_completion_timestamps'][7] = turn_index
        elif current_step == 8: # Pincode Confirmation
            if entities.get('pincode') is not None and any(conf in entities['confirmations'] for conf in ['yes', 'हां', 'हाँ', 'correct', 'yeah', 'जी']): # Added जी
                 if 8 not in self.conversation_state['completed_steps']:
                    self.conversation_state['completed_steps'].append(8)
                    self.conversation_state['step_completion_timestamps'][8] = turn_index
        elif current_step == 9: # Physical Activity Inquiry
            if entities.get('physical_activity_status') or \
               (entities.get('confirmations') and any(conf in entities['confirmations'] for conf in ['जी', 'yes', 'no', 'walking', 'working'])):
                 if 9 not in self.conversation_state['completed_steps']:
                    self.conversation_state['completed_steps'].append(9)
                    self.conversation_state['step_completion_timestamps'][9] = turn_index
        elif current_step == 11: # Pre-existing Conditions Inquiry
            if entities.get('medical_conditions') or \
               (entities.get('confirmations') and any(conf in entities['confirmations'] for conf in ['yes', 'no', 'कुछ भी नहीं है', 'nothing'])):
                if 11 not in self.conversation_state['completed_steps']:
                    self.conversation_state['completed_steps'].append(11)
                    self.conversation_state['step_completion_timestamps'][11] = turn_index
        elif current_step == 12: # Current Medications Inquiry
            if entities.get('medications_status') or \
               (entities.get('confirmations') and any(conf in entities['confirmations'] for conf in ['yes', 'no', 'never'])):
                 if 12 not in self.conversation_state['completed_steps']:
                    self.conversation_state['completed_steps'].append(12)
                    self.conversation_state['step_completion_timestamps'][12] = turn_index
        elif current_step == 13: # Hospitalization History Inquiry
            if entities.get('hospitalization_history') or \
               (entities.get('confirmations') and any(conf in entities['confirmations'] for conf in ['yes', 'no', 'never', 'January'])):
                 if 13 not in self.conversation_state['completed_steps']:
                    self.conversation_state['completed_steps'].append(13)
                    self.conversation_state['step_completion_timestamps'][13] = turn_index
        elif current_step == 14: # Doctor Recommendation Inquiry
            if entities.get('doctor_recommendation_status') or \
               (entities.get('confirmations') and any(conf in entities['confirmations'] for conf in ['yes', 'no'])):
                 if 14 not in self.conversation_state['completed_steps']:
                    self.conversation_state['completed_steps'].append(14)
                    self.conversation_state['step_completion_timestamps'][14] = turn_index
        elif current_step == 15: # Policy Suggestion
            if entities.get('policy_suggested') or \
               (entities.get('confirmations') and any(conf in entities['confirmations'] for conf in ['yes', 'हां', 'हाँ', 'boliye', 'bolie bolie', 'interested'])):
                if 15 not in self.conversation_state['completed_steps']:
                    self.conversation_state['completed_steps'].append(15)
                    self.conversation_state['step_completion_timestamps'][15] = turn_index
        elif current_step == 17: # Policy Details Presentation
            if entities.get('confirmations') and any(conf in entities['confirmations'] for conf in ['yes', 'हां', 'हाँ', 'ok', 'ठीक है']):
                if 17 not in self.conversation_state['completed_steps']:
                    self.conversation_state['completed_steps'].append(17)
                    self.conversation_state['step_completion_timestamps'][17] = turn_index
        elif current_step == 19: # Policy Summary and Confirmation
            if entities.get('confirmation_to_proceed_application') or \
               (entities.get('confirmations') and any(conf in entities['confirmations'] for conf in ['yes', 'हां', 'हाँ', 'bolie bolie', 'ji hai'])):
                if 19 not in self.conversation_state['completed_steps']:
                    self.conversation_state['completed_steps'].append(19)
                    self.conversation_state['step_completion_timestamps'][19] = turn_index
        elif current_step == 20: # Quotation
            if entities.get('callback_agreement') or \
               (entities.get('confirmations') and any(conf in entities['confirmations'] for conf in ['ok', 'हां', 'हाँ'])):
                if 20 not in self.conversation_state['completed_steps']:
                    self.conversation_state['completed_steps'].append(20)
                    self.conversation_state['step_completion_timestamps'][20] = turn_index
        elif current_step == 22: # Schedule Callback
            if entities.get('callback_time') or entities.get('callback_date') or \
               (entities.get('confirmations') and any(conf in entities['confirmations'] for conf in ['ok', 'हां', 'हाँ', 'ठीक है', 'ji', 'yes', 'sure', 'yeah'])):
                if 22 not in self.conversation_state['completed_steps']:
                    self.conversation_state['completed_steps'].append(22)
                    self.conversation_state['step_completion_timestamps'][22] = turn_index

    def get_conversation_flow_data(self) -> Dict[str, Any]:
        """Get comprehensive conversation flow data for visualization."""
        return {
            'completed_steps': self.conversation_state['completed_steps'],
            'step_transitions': self.conversation_state['step_transitions'],
            'collected_entities': self.conversation_state['collected_entities'],
            'current_step': self.conversation_state['current_step'],
            'completion_timestamps': self.conversation_state['step_completion_timestamps'],
            'bot_patterns': self.bot_dialogue_patterns,
            'human_patterns': self.human_response_patterns
        }

class EnhancedABHIVisualizationDashboard:
    """
    Enhanced visualization dashboard with more accurate data processing and
    additional insights based on the improved NLP analysis.
    Supports combined visualizations for multiple customers.
    """

    def __init__(self, customer_reports: Dict[str, Dict], nlp_processor: EnhancedNLPProcessor, prompt_rules: Dict):
        """
        Initializes the dashboard with data for multiple customers.
        :param customer_reports: A dictionary where keys are customer names and values are dicts
                                 containing 'processed_transcript_df' and 'call_report_data'.
        :param nlp_processor: The NLP processor instance.
        :param prompt_rules: The prompt rules dictionary.
        """
        self.customer_reports = customer_reports
        self.nlp_processor = nlp_processor
        self.prompt_rules = prompt_rules
        self.colors = px.colors.qualitative.Set3 # A color palette

    def create_enhanced_conversation_flow_heatmap(self):
        """
        Create an accurate conversation flow heatmap based on actual step transitions
        detected by the enhanced NLP system, combined for all customers.
        """
        # Build transition matrix from actual flow data across all customers
        step_transitions = defaultdict(lambda: defaultdict(int))

        for customer_name, report_data in self.customer_reports.items():
            flow_data = report_data.get('call_report_data', {}).get('conversation_flow_data', {})
            transitions = flow_data.get('step_transitions', [])

            for transition in transitions:
                from_step = f"Step {transition['from_step']}"
                to_step = f"Step {transition['to_step']}"
                step_transitions[from_step][to_step] += 1

        # If no transitions found, try to infer from completed steps sequence
        if not any(step_transitions.values()):
            for customer_name, report_data in self.customer_reports.items():
                flow_data = report_data.get('call_report_data', {}).get('conversation_flow_data', {})
                completed_steps_sequence = sorted(flow_data.get('completed_steps', []),
                                                  key=lambda x: flow_data['step_completion_timestamps'].get(x, float('inf')))
                for i in range(len(completed_steps_sequence) - 1):
                    from_step = f"Step {completed_steps_sequence[i]}"
                    to_step = f"Step {completed_steps_sequence[i+1]}"
                    step_transitions[from_step][to_step] += 1

        # Convert to matrix format
        all_steps = set()
        for from_step in step_transitions:
            all_steps.add(from_step)
            for to_step in step_transitions[from_step]:
                all_steps.add(to_step)

        if not all_steps:
            fig = go.Figure()
            fig.add_annotation(
                text="No step transitions detected across all conversations.",
                xref="paper", yref="paper",
                x=0.5, y=0.5, xanchor='center', yanchor='middle',
                showarrow=False, font=dict(size=14)
            )
            fig.update_layout(title="Combined Conversation Flow Heatmap", height=400)
            return fig

        # Ensure all_steps are sorted numerically
        all_steps_list = sorted(list(all_steps), key=lambda x: int(x.split(' ')[1]) if x.startswith('Step ') and x.split(' ')[1].isdigit() else float('inf'))
        matrix = np.zeros((len(all_steps_list), len(all_steps_list)))

        for i, from_step in enumerate(all_steps_list):
            for j, to_step in enumerate(all_steps_list):
                matrix[i][j] = step_transitions[from_step][to_step]

        # Create heatmap
        fig = go.Figure(data=go.Heatmap(
            z=matrix,
            x=all_steps_list,
            y=all_steps_list,
            colorscale='Viridis',
            text=matrix.astype(int),
            texttemplate="%{text}",
            textfont={"size": 10},
            hoverongaps=False
        ))

        fig.update_layout(
            title="Combined Conversation Flow Heatmap (Actual Transitions)",
            xaxis_title="Next Step",
            yaxis_title="Current Step",
            height=600,
            width=800,
            xaxis={'side': 'bottom'},
            yaxis={'autorange': 'reversed'}
        )

        return fig

    def create_enhanced_policy_entity_chart(self):
        """
        Create an accurate chart showing policy mentions detected by the enhanced NLP system
        across all customers.
        """
        all_policy_mentions_data = []
        for customer_name, report_data in self.customer_reports.items():
            collected_entities = report_data.get('call_report_data', {}).get('collected_entities', {})
            policies_mentioned = collected_entities.get('policies_mentioned', [])
            for policy in policies_mentioned:
                all_policy_mentions_data.append({'Customer': customer_name, 'Policy': policy})

        if not all_policy_mentions_data:
            fig = go.Figure()
            fig.add_annotation(
                text="No policy mentions detected in any conversation.",
                xref="paper", yref="paper",
                x=0.5, y=0.5, xanchor='center', yanchor='middle',
                showarrow=False, font=dict(size=14)
            )
            fig.update_layout(title="Combined Policy Mentions Across Conversations", height=400)
            return fig

        policy_df = pd.DataFrame(all_policy_mentions_data)
        # Count mentions per policy per customer
        policy_counts = policy_df.groupby(['Customer', 'Policy']).size().reset_index(name='Count')

        fig = px.bar(
            policy_counts,
            x='Policy',
            y='Count',
            color='Customer', # Use customer name as color/legend
            title='Combined Policy Mentions Across Conversations',
            color_discrete_sequence=self.colors,
            text='Count',
            barmode='group' # Group bars by policy, show customer bars next to each other
        )
        fig.update_traces(texttemplate='%{text}', textposition='outside')
        fig.update_layout(xaxis_title="Policy Type", yaxis_title="Number of Mentions", height=600)
        return fig

    def create_sentiment_over_time_chart(self):
        """
        Visualize sentiment polarity over the course of the conversation for all customers.
        Each customer will have a separate line.
        """
        all_sentiment_data = []
        for customer_name, report_data in self.customer_reports.items():
            df = report_data['processed_transcript_df'].copy()
            if not df.empty:
                df['Customer'] = customer_name
                all_sentiment_data.append(df)

        if not all_sentiment_data:
            fig = go.Figure()
            fig.add_annotation(
                text="No sentiment data available for any conversation.",
                xref="paper", yref="paper",
                x=0.5, y=0.5, xanchor='center', yanchor='middle',
                showarrow=False, font=dict(size=14)
            )
            fig.update_layout(title="Combined Sentiment Over Time", height=400)
            return fig

        combined_df = pd.concat(all_sentiment_data)

        fig = px.line(
            combined_df,
            x='turn_index',
            y='sentiment_compound',
            color='Customer', # Separate lines for each customer
            line_dash='speaker', # Different dash styles for Human/Chat Bot
            title='Combined Sentiment Polarity Over Time',
            labels={'turn_index': 'Conversation Turn', 'sentiment_compound': 'Sentiment Polarity (Compound)'},
            hover_data={'utterance': True, 'sentiment_emotion': True, 'speaker': True},
            color_discrete_sequence=px.colors.qualitative.Dark24 # Use a larger color palette for multiple customers/speakers
        )
        fig.update_layout(yaxis_range=[-1, 1], height=600)
        fig.add_hline(y=0.05, line_dash="dot", line_color="green", annotation_text="Positive Threshold")
        fig.add_hline(y=-0.05, line_dash="dot", line_color="red", annotation_text="Negative Threshold")
        return fig

    def create_language_distribution_chart(self):
        """
        Visualize the distribution of languages used by Human and Chat Bot, combined for all customers.
        """
        all_lang_data = []
        for customer_name, report_data in self.customer_reports.items():
            df = report_data['processed_transcript_df'].copy()
            if not df.empty:
                df['Customer'] = customer_name
                all_lang_data.append(df)

        if not all_lang_data:
            fig = go.Figure()
            fig.add_annotation(
                text="No language data available for any conversation.",
                xref="paper", yref="paper",
                x=0.5, y=0.5, xanchor='center', yanchor='middle',
                showarrow=False, font=dict(size=14)
            )
            fig.update_layout(title="Combined Language Distribution", height=400)
            return fig

        combined_df = pd.concat(all_lang_data)
        lang_counts = combined_df.groupby(['Customer', 'speaker', 'language']).size().reset_index(name='count')

        fig = px.bar(
            lang_counts,
            x='speaker',
            y='count',
            color='language',
            facet_col='Customer', # Create separate columns for each customer
            title='Combined Language Distribution by Speaker and Customer',
            labels={'count': 'Number of Utterances', 'speaker': 'Speaker', 'language': 'Language'},
            color_discrete_sequence=self.colors,
            text='count',
            barmode='stack' # Stack languages within each speaker/customer group
        )
        fig.update_traces(texttemplate='%{text}', textposition='outside')
        fig.update_layout(height=600)
        return fig

    def create_entity_extraction_overview(self):
        """
        Visualize an overview of extracted entities across all customers.
        """
        all_entities_data = []
        for customer_name, report_data in self.customer_reports.items():
            df = report_data['processed_transcript_df'].copy()
            if not df.empty:
                for _, row in df.iterrows():
                    entities = row['extracted_entities']
                    for ent_type, ent_val in entities.items():
                        if ent_val and ent_type not in ['confirmations', 'intent_indicators']:
                            if isinstance(ent_val, list):
                                for item in ent_val:
                                    all_entities_data.append({'Customer': customer_name, 'Entity Type': ent_type, 'Entity Value': item})
                            else:
                                all_entities_data.append({'Customer': customer_name, 'Entity Type': ent_type, 'Entity Value': ent_val})

        if not all_entities_data:
            fig = go.Figure()
            fig.add_annotation(
                text="No specific entities extracted across all conversations.",
                xref="paper", yref="paper",
                x=0.5, y=0.5, xanchor='center', yanchor='middle',
                showarrow=False, font=dict(size=14)
            )
            fig.update_layout(title="Combined Extracted Entities Overview", height=400)
            return fig

        entity_df_raw = pd.DataFrame(all_entities_data)
        entity_df_raw['Full_Entity_Label'] = entity_df_raw['Entity Type'] + ': ' + entity_df_raw['Entity Value'].astype(str)

        # Count occurrences of each unique entity across all customers
        entity_counts = entity_df_raw.groupby('Full_Entity_Label').size().reset_index(name='Count')
        entity_counts = entity_counts.sort_values(by='Count', ascending=False).head(20) # Top 20 entities overall

        # Now, for plotting, merge back with customer data to allow coloring by customer for the top entities
        plot_df = pd.merge(entity_counts, entity_df_raw, on='Full_Entity_Label', how='left')
        plot_df = plot_df.groupby(['Full_Entity_Label', 'Customer']).size().reset_index(name='Count_Per_Customer')

        fig = px.bar(
            plot_df,
            x='Count_Per_Customer',
            y='Full_Entity_Label',
            color='Customer', # Color by customer
            orientation='h',
            title='Top Extracted Entities Overview (Combined)',
            labels={'Count_Per_Customer': 'Frequency', 'Full_Entity_Label': 'Entity Type: Value'},
            color_discrete_sequence=self.colors,
            text='Count_Per_Customer',
            barmode='stack' # Stack bars for each customer for a given entity
        )
        fig.update_traces(texttemplate='%{text}', textposition='outside')
        fig.update_layout(yaxis={'categoryorder':'total ascending'}, height=700)
        return fig

    def create_step_completion_timeline(self):
        """
        Visualize when each step was completed in the conversation for all customers.
        Each customer will have a separate line of markers.
        """
        all_timeline_data = []
        for customer_name, report_data in self.customer_reports.items():
            flow_data = report_data.get('call_report_data', {}).get('conversation_flow_data', {})
            completion_timestamps = flow_data.get('completion_timestamps', {})

            if completion_timestamps:
                # Sort steps numerically for consistent display
                sorted_steps = sorted(completion_timestamps.keys())
                for step in sorted_steps:
                    turn_index = completion_timestamps[step]
                    all_timeline_data.append({'Customer': customer_name, 'Step': f"Step {step}", 'Turn Index': turn_index})

        if not all_timeline_data:
            fig = go.Figure()
            fig.add_annotation(
                text="No step completion data available for any conversation.",
                xref="paper", yref="paper",
                x=0.5, y=0.5, xanchor='center', yanchor='middle',
                showarrow=False, font=dict(size=14)
            )
            fig.update_layout(title="Combined Step Completion Timeline", height=400)
            return fig

        timeline_df = pd.DataFrame(all_timeline_data).sort_values(by='Turn Index')

        fig = px.scatter(
            timeline_df,
            x='Turn Index',
            y='Step',
            text='Step',
            color='Customer', # Separate colors for each customer
            title='Combined Conversation Step Completion Timeline',
            labels={'Turn Index': 'Conversation Turn Index', 'Step': 'Completed Step'},
            color_discrete_sequence=self.colors
        )
        fig.update_traces(mode='markers+text', textposition='top center')
        fig.update_layout(height=600)
        return fig

    def analyze_prompt_adherence_and_correctness(self) -> pd.DataFrame:
        """
        Performs an analysis across all transcripts to check prompt adherence
        and the correctness of bot's policy suggestions/outcomes.
        """
        analysis_results = []

        for customer_name, report_data in self.customer_reports.items():
            call_report = report_data['call_report_data']
            collected_entities = call_report['collected_entities']
            conversation_flow_data = call_report['conversation_flow_data']

            # --- 1. Flow Adherence ---
            flow_adherence = "Adherent"
            completed_steps = sorted(conversation_flow_data['completed_steps'])

            # Check for non-sequential jumps or excessive backtracking.
            if len(completed_steps) > 1:
                for i in range(len(completed_steps) - 1):
                    # Define steps where jumps are expected/allowed (e.g., fallback steps, end of call)
                    allowed_jump_targets = [41, 22] # End call, Schedule Callback
                    allowed_jump_from_steps = [2, 10, 11, 12, 13, 14, 25, 31, 32, 33, 34, 36, 37, 38, 39, 40]

                    if completed_steps[i+1] < completed_steps[i]: # Backtracking/Loop
                        flow_adherence = "Partial Adherence (Backtracking/Loop)"
                        break
                    elif completed_steps[i+1] > completed_steps[i] + 1: # Skipped steps (non-sequential forward)
                        if completed_steps[i+1] not in allowed_jump_targets and \
                           completed_steps[i] not in allowed_jump_from_steps:
                            flow_adherence = "Partial Adherence (Skipped Steps)"
                            break
                        # Additional check for large, unexpected skips
                        if (completed_steps[i+1] - completed_steps[i]) > 5: # Arbitrary large jump
                             flow_adherence = "Partial Adherence (Large Skip)"
                             break


            # Check for repetition rule adherence
            bot_utterances_texts = [p['utterance'] for p in conversation_flow_data['bot_patterns']]
            if self._detect_repetition(bot_utterances_texts):
                if "Adherent" in flow_adherence: # If not already marked by a worse adherence issue
                    flow_adherence = "Partial Adherence (Repetition)"

            # --- 2. Policy Suggestion Correctness ---
            expected_policy = self._determine_expected_policy(collected_entities)
            bot_suggested_policy = call_report.get('bot_suggested_policy')

            policy_correctness = "Not Applicable" # Default if no policy was expected or suggested
            if expected_policy:
                if bot_suggested_policy == expected_policy:
                    policy_correctness = "Correct"
                else:
                    policy_correctness = f"Incorrect (Expected: {expected_policy}, Bot: {bot_suggested_policy if bot_suggested_policy else 'None'})"
            elif bot_suggested_policy: # Bot suggested something when no clear policy was expected based on rules
                 policy_correctness = f"Unexpected Suggestion (Bot: {bot_suggested_policy})"

            # --- 3. Expected Outcome Achieved ---
            outcome_achieved = "Not Achieved" # Default to not achieved
            final_step = conversation_flow_data['current_step']

            # Check for successful completion based on different possible end states in your prompt
            if final_step == 41: # Goodbye/Call End
                outcome_achieved = "Call Ended Gracefully"

            if 22 in completed_steps: # Callback scheduled
                 outcome_achieved = "Callback Scheduled"

            # If a correct policy was suggested AND the conversation ended gracefully or moved towards application/quotation
            if policy_correctness == "Correct" and final_step in [16, 17, 18, 19, 20, 21, 41]: # Moved to post-suggestion steps
                outcome_achieved = "Policy Suggested & Advanced"
            elif policy_correctness == "Correct": # Policy correctly suggested but conversation ended without further progress
                outcome_achieved = "Policy Suggested (Call Ended Prematurely)"

            # If no policy was suggested but call ended gracefully (e.g., human disinterest)
            elif not bot_suggested_policy and final_step == 41 and 25 in completed_steps: # Ended due to disinterest
                outcome_achieved = "Call Ended Gracefully (Customer Disinterest)"
            elif not bot_suggested_policy and final_step == 41:
                outcome_achieved = "Call Ended Gracefully (No Policy Suggested)"


            analysis_results.append({
                'Customer Name': customer_name,
                'Total Turns': call_report['total_turns'],
                'Flow Adherence': flow_adherence,
                'Policy Suggestion Correctness': policy_correctness,
                'Outcome Achieved': outcome_achieved,
                'Final Step': final_step,
                'Bot Suggested Policy': bot_suggested_policy,
                'Expected Policy (Inferred)': expected_policy,
                # 'Collected Entities': collected_entities # Can be re-enabled for detailed debugging
            })

        return pd.DataFrame(analysis_results)

    def _determine_expected_policy(self, entities: Dict) -> Optional[str]:
        """
        Infers the expected policy based on collected entities and prompt rules (Step 15 conditions).
        """
        age = entities.get('age')
        age_months = entities.get('age_months') # For cases like "5 months old"

        # If age is 0 and age_months is present, it means very young, effectively 0 years old
        if age == 0 and age_months is not None:
            pass # Keep age as 0

        medical_conditions = entities.get('medical_conditions', [])
        # Check for any pre-existing disease (PED)
        # Check against lowercased values for robustness
        has_ped = len(medical_conditions) > 0 or \
                  (entities.get('medications_status') and entities['medications_status'].lower() not in ['no', 'none', 'नहीं', 'नही']) or \
                  (entities.get('hospitalization_history') and entities['hospitalization_history'].lower() not in ['no', 'none', 'never']) or \
                  (entities.get('doctor_recommendation_status') and entities['doctor_recommendation_status'].lower() not in ['no', 'none'])

        # Policy Flow Conditions from prompt (Step 15)
        # • Strictly If the user or any family member has a Pre-Existing Disease (PED), then go to Step 15.1 and recommend the FIRST policy: Activ One Vital .
        if has_ped:
            return "Activ One Vital"

        # If no PED:
        # • Strictly If no one has PED and any one family member's age is ≥ 55, then go to Step 15.4 and recommend the FIRST policy: Activ One Maxplus.
        # • Strictly If no one has PED and any one family member's age is between 36 and 54 (inclusive of 36, exclusive of 55), then go to Step 15.3 and recommend the FIRST policy: Activ One Maxplus.
        # • Strictly If no one has PED and all family members are aged ≤ 35, then go to Step 15.2 and recommend the FIRST policy: Activ Fit.

        effective_age = age
        # Assuming `family_members_ages` might be a list of ages if extracted
        if 'family_members_ages' in entities and isinstance(entities['family_members_ages'], list):
            all_ages = [a for a in entities['family_members_ages'] if a is not None]
            if age is not None: # Include primary applicant's age if available
                all_ages.append(age)
            if all_ages:
                effective_age = max(all_ages)

        # Special handling for Chetan's child's age (5 months, which results in age 0)
        # If effective_age is 0, it should fall into <=35 category.

        if effective_age is not None:
            if effective_age >= 55:
                return "Activ One Maxplus"
            elif 36 <= effective_age <= 54:
                return "Activ One Maxplus"
            elif effective_age <= 35: # This includes age 0 (5 months old)
                return "Activ Fit"

        # • Strictly if the customer has not shared any details about pre-existing diseases or
        # medications, hospitalization history and also personal details like age or family
        # member details, but they still want to know about the policy then suggest them "Activ One N-X-T".
        # This condition is typically a fallback when critical information is missing.
        # If no PED and no effective age was collected, then suggest Activ One N-X-T.
        if not has_ped and effective_age is None:
             return "Activ One N-X-T"

        return None # No specific policy can be strictly inferred from prompt conditions

    def _detect_repetition(self, utterances: List[str], window_size: int = 2, similarity_threshold: float = 0.95) -> bool:
        """
        Detects if the bot repeatedly asks the same question or states the same intent.
        Looks for highly similar consecutive utterances.
        """
        if len(utterances) < 2:
            return False

        # Filter out very short or non-informative utterances before checking similarity
        filtered_utterances = [u for u in utterances if len(u.split()) > 3] # Only consider utterances with more than 3 words

        if len(filtered_utterances) < 2:
            return False

        vectorizer = TfidfVectorizer(max_features=1000)

        # Fit on all filtered utterances to get a consistent vocabulary
        try:
            vectorizer.fit(filtered_utterances)
        except ValueError: # Handle case where all filtered utterances are too similar or empty
            return False

        for i in range(len(filtered_utterances) - 1):
            u1_text = filtered_utterances[i].lower()
            u2_text = filtered_utterances[i+1].lower()

            if not u1_text or not u2_text:
                continue

            u1_vector = vectorizer.transform([u1_text])
            u2_vector = vectorizer.transform([u2_text])

            # Check if vectors are non-zero before calculating similarity
            if u1_vector.nnz > 0 and u2_vector.nnz > 0:
                if cosine_similarity(u1_vector, u2_vector)[0, 0] >= similarity_threshold:
                    return True
        return False


def extract_customer_name(transcript_text: str) -> str:
    """Extracts the customer name from the first line of the transcript."""
    first_line = transcript_text.strip().split('\n')[0]
    # Pattern for "क्या मैं <name> से बात कर रही हूँ?"
    match = re.search(r"क्या मैं ([\w\s]+) से बात कर रही हूँ\?", first_line)
    if match:
        return match.group(1).strip()

    # Pattern for "क्या मैं <name> से बात कर रहा हूँ?" (male speaker)
    match_alt = re.search(r"क्या मैं ([\w\s]+) से बात कर रहा हूँ\?", first_line)
    if match_alt:
        return match_alt.group(1).strip()

    return "Unknown Customer" # Default if name not found


# --- Main Execution ---

if __name__ == "__main__":
    # Define multiple raw transcripts
    transcripts_raw = [
        # Transcript 1 (Mukesh)
        """
Chat Bot: नमस्ते,मैं Kavya बोल रही हूँ aditya birla health insurance से, क्या मैं mukesh से बात कर रही हूँ?
Human: Hello hello
Human: Hello.
Chat Bot: Hello, I am Kavya from Aditya Birla Health Insurance.
Chat Bot: Please note this call is being recorded for quality and monitoring purposes.
Chat Bot: Hope that's fine?
Human: बोलिए बोलिए.
Chat Bot: हमें आपकी request Aditya Birla Health Insurance website से मिली है.
Chat Bot: तो मैं बस यह check करना चाहती थी कि क्या आप fresh policy की तलाश में हैं, या अपनी existing policy को port करने के बारे में सोच रहे हैं?
Human: Quality की class में है
Chat Bot: ठीक है, तो आप एक नई health insurance policy लेना चाहते हैं, correct?
Human: Yes.
Chat Bot: Great, and to proceed further, may I know your age?
Human: Twenty twenty four
Chat Bot: Thank you for sharing that.
Chat Bot: Just to confirm, is Four Eight Four Six Six One the correct pincode for your city?
Human: हां हां. Four four eight अड़तालीस चवालीस इकसठ है
Chat Bot: अच्छा, क्या आप कोई regular physical activity करते हैं, जैसे walking, yoga, gym, या कुछ similar?
Human: जी.
Chat Bot: क्या आपकी कोई pre-existing health conditions हैं, जैसे asthma, diabetes, blood pressure, heart issues, या कुछ और जिसके बारे में हमें जानना चाहिए?
Human: No no no कुछ.
Chat Bot: <silence>
Chat Bot: क्या आप currently किसी health condition के
Human: कुछ भी नहीं है.
Chat Bot: <silence>
Chat Bot: अच्छा, क्या आप कभी अस्पताल में भर्ती हुए हैं?
Chat Bot: Hello?
Human: No.
Chat Bot: क्या किसी doctor ने recently किसी medical condition के लिए surgery या treatment recommend किया है?
Chat Bot: Hello?
Human: नहीं नहीं नहीं.
Human: नहीं नहीं.
Chat Bot: ## Step 15: Policy Suggestion
Chat Bot: समझ गई, धन्यवाद जानकारी के लिए.
Chat Bot: Based on the details you've shared, मैं "Activ Fit" policy की सलाह दूंगी.
Chat Bot: यह policy specially young और healthy individuals के लिए designed है, जो कम premium में ज्यादा rewards चाहते हैं.
Chat Bot: क्या आप इसके बारे में अधिक जानने में interested हैं?
Chat Bot: Hello?
Human: Yes yes बोलिए.
Human: हां हां बोलिए ना.
Chat Bot: If you choose the Activ Fit policy, the initial waiting period will be thirty
Chat Bot: <silence>
Chat Bot: अगर आप "Activ Fit" policy लेते हैं, तो इसका initial waiting period thirty days का होगा, pre-existing diseases का waiting period three years का होगा, और specific diseases का waiting period two years का होगा.
Chat Bot: क्या ये information आपकी help कर रही है?
Human: हां.
Chat Bot: अच्छा, मैं आपको Activ Fit के
Human: Ok ok ठीक है.
Chat Bot: <silence>
Chat Bot: हमारे discussion के basis पर, इस policy में features हैं जैसे Health Returns, Claim Protect, Super Reload, और Binge Refill.
Chat Bot: क्या आप अब application के साथ आगे बढ़ना चाहेंगे?
Chat Bot: Hello?
Human: हां बोलिए बोलिए.
Chat Bot: Great, हमारे पास आपका email address है sahumukesheight
Human: हां बोलिए ना बोलिए ना.
Chat Bot: <silence>
Chat Bot: हमारे पास आपका email address है sahumukesheight eight one seven at the rate gmail dot com, क्या यह correct है?
Human: Ok ok.
Chat Bot:
Human: हां
Chat Bot: जैसे ही आप insurance quotation जो हमने भेजा है उसे review कर लेंगे, मैं आपके convenience के अनुसार एक callback schedule कर दूंगी ताकि हम next steps discuss कर सकें.
Chat Bot: आप एक convenient समय बताएँ ताकि मैं callback schedule कर सकूं?
Human: Ok ok.
Chat Bot: क्या यह सही रहेगा अगर मैं आपको कल दो बजे call करूं, या आप सुबह ग्यारह से शाम छह बजे के बीच कोई और time prefer करेंगे?
Human: नहीं सुबह से करना sir सुबह से खाली रहेगा
Human: Ok.
Chat Bot: Are you there?
Chat Bot: कोई problem नहीं.
Chat Bot: सुबह ग्यारह से शाम छह के बीच कौन सा समय आपके लिए सबसे convenient रहेगा?
Human: Ya lo voy checar.
Chat Bot: Lo siento, pero actualmente solo puedo asistir en inglés o hindi.
Chat Bot: ¿Le gustaría que organice una llamada de un asesor específico para su idioma?
Chat Bot: "
Chat Bot: Hello?
Chat Bot: Hello?
Human: हां हां बोलिए.
Chat Bot: Great! मैं कल दोपहर Gyaarah Baje पर callback schedule कर रही हूँ. अगर आपके और कोई
Human: हां हां बोलिए ना.
Chat Bot: <silence>
Chat Bot: आपका time के लिए धन्यवाद.
Chat Bot: मैं scheduled time पर follow up करूंगी.
Chat Bot: अगर आपके पास और कोई questions हैं, तो हमें संपर्क करें.
Chat Bot: Have a great day. Goodbye.
        """,
        # Transcript 2 (Swayam)
        """
Chat Bot: नमस्ते, मैं Rahul बोल रहा हूँ aditya birla health insurance से, क्या मैं Swayam से बात कर रहा हूँ?
Human: Yes. This is Sam.
Chat Bot: Thank you, Sam.
Chat Bot: Please note इस call को quality और monitoring purpose के लिए record किया जा रहा है.
Chat Bot: Hope that's fine?
Human: Yeah. That's fine.
Chat Bot: Actually हमें आपकी request Aditya Birla Health Insurance website से मिली है.
Chat Bot: तो मैं बस यह check करना चाहता था कि क्या आप fresh policy की तलाश में हैं, या अपनी existing policy को port करने के बारे में सोच रहे हैं?
Human: No. I don't have a policy as of now.
Chat Bot: अच्छा, तो आप नई health insurance policy लेना चाहते हैं। आपको better guide करने के लिए - क्या आप सिर्फ अपने लिए coverage चाहते हैं, या family members को भी include करना चाहते हैं?
Human: No. So I wanted to understand, like, doesn't this really work after I grow to a certain age like today I have no health condition but later on I may need some cover. So I just wanted to understand why I need health insurance.
Chat Bot: Health insurance आपके लिए एक financial safety net की तरह काम करता है, जो आपको unexpected medical expenses से protect करता है.
Chat Bot: आज भले ही आपको कोई health condition न हो, लेकिन future में health risks बढ़ सकते हैं जैसे age increase होने के साथ, और कभी-कभी अचानक hospitalization या medical treatment की जरूरत भी पड़ सकती है, जो काफी महंगी हो सकती है.
Chat Bot: Health insurance की वजह से न सिर्फ आप financial burdens से बचे रह सकते हैं, बल्कि आप time पर और बिना चिंता किए आवश्यक medical care भी ले सकते हैं.
Chat Bot: इसके साथ ही, कुछ policies health management benefits भी offer करती हैं जैसे annual health checkups, जिसके तहत आप अपनी health का regular checkup maintain कर सकते हैं.
Chat Bot: बीमारी या medical emergencies कभी भी आ सकती हैं.
Chat Bot: Health insurance आपके लिए एक financial safety net की तरह काम करता है, जो आपको
Human: So how much will it so how much will you charge me with?
Chat Bot: Great, आगे बढ़ने के लिए आपकी कुछ details चाहिए होंगी जिससे कि हम आपकी specific needs के अनुसार सही policy recommend कर सकें। क्या मैं आपकी age जान सकता हूँ?
Human: No. I shared my age, but, like, what if I change job or I'm changing locations? Would my policy still old?
Chat Bot: Yes, absolutely. Once you have a health insurance policy, it remains valid even if you change jobs or relocate. Your policy is with you regardless of your employment status or location. You just need to make sure to inform us about your new address or any other contact details so that your records stay updated for any communication. Do you have any other questions or would you like to proceed with exploring policy
Human: So let's say I make the payment today, you will not ask me for money again next year or the year after that, right? Like once I make a health insurance payment, it there. Like like like a life insurance.
Chat Bot: <silence>
Chat Bot: Health insurance का purpose different होता है.
Chat Bot: Health insurance एक annual contract की तरह होता है, जिसका मतलब है कि आपको हर साल policy renew करनी होगी और इसके लिए annual premium pay करना होगा.
Chat Bot: Health insurance life insurance की तरह one-time payment या whole life coverage की तरह नहीं होता है.
Chat Bot: Annual renewal की process से policyholder की needs, premium, और coverages को साल दर साल adjust किया जा सकता है.
Chat Bot: इस तरीके से, आप अपनी policy में बदलाव भी कर सकते हैं, जैसे coverage amount adjust करना या add-on benefits लेना.
Chat Bot: अगर आप ज्यादा details में जानना चाहते हैं या एक specific quote देखना चाहते हैं, तो मैं आपकी age और कुछ basic information collect करके आपको best options बता सकता हूं.
Human: No. So, like, can I sort of make a payment today? But, you know, because I don't have a condition, can I pause it and then restart it, let's say, one or two years later?
Chat Bot: Health insurance policies generally can't be paused and restarted like that.
Chat Bot: They need to be maintained continuously through regular premium payments.
Chat Bot: If you skip a renewal, the policy lapses, and you lose all accumulated benefits, like waiting period credit and coverage continuity.
Chat Bot: If you choose to get a policy again later, you'd need to reapply and go through the waiting periods anew.
Chat Bot: It's often more beneficial to keep your policy active consistently to maintain continuous coverage, especially since health issues can arise unexpectedly.
Chat Bot: Do you have any other questions about health insurance policies or coverage?
Human: So can I add my partner, like, after I get married?
Chat Bot: Yes, absolutely! After you get married, you can definitely add your partner to your existing health insurance policy.
Chat Bot: This typically involves notifying your insurance provider about the change in your family status and submitting any required documents related to your marriage.
Chat Bot: It's a good way to ensure that both you and your partner have comprehensive health coverage under one plan.
Chat Bot: If you need help with this process or have any more questions, feel free to ask!
Human: So, like, today, have bought a plan and, you know, there's some waiting period that I've already, you know, sort of waited for. And then I get married. So do that applies to my partner as well. Like, she will not have to then go through the waiting period again.
Chat Bot: When you add your partner to your existing health insurance policy, she would typically have her own waiting periods that would need to be completed, even if you have already completed yours.
Chat Bot: The waiting periods apply individually to each policy member.
Chat Bot: However, some insurance providers might offer certain terms or discounts based on your existing coverage, so it's always good to check the specific terms with your insurance provider.
Chat Bot: If you have any questions or need further assistance on this, feel free to ask.
Human: So who is this insurance provider who can give me this?
Chat Bot: मैं सिर्फ Aditya Birla Health Insurance का Al representative हूं.
Chat Bot: क्या आपको Health Insurance से जुड़ा कोई doubt है?
Human: So which insurance provider will allow give me this discount?
Human: Tata दे देगा यह?
Chat Bot: मैं सिर्फ Aditya Birla Health Insurance का representative हूं.
Chat Bot: क्या आपको Aditya Birla Health Insurance के बारे में कोई doubt है?
Human: Link with our Thank you.
Human: So much.
Human: Bye.
Chat Bot: Goodbye.
        """,
        # Transcript 3 (Ibrahim)
        """
Chat Bot: नमस्ते, मैं Rahul बोल रहा हूँ aditya birla health insurance से, क्या मैं Ibrahim से बात कर रहा हूँ?
Human: हां जी बोलिए.
Chat Bot: धन्यवाद. कृपया ध्यान दें कि यह कॉल quality और monitoring purpose के लिए record की जा रही है.
Chat Bot: आशा करता हूँ कि आपको यह ठीक लगेगा?
Chat Bot: Are you there?
Human: Person you are speaking
Chat Bot: Actually हमें आपकी request Aditya Birla Health Insurance website से मिली है.
Chat Bot: तो मैं यह check करना चाहता था कि क्या आप fresh policy की तलाश में हैं, या अपनी existing policy को port करने के बारे में सोच रहे हैं?
Human: fresh policy के लिए.
Chat Bot: अच्छा, तो आप नई health insurance policy लेना चाहते हैं.
Chat Bot: आपको better guide करने के लिए - क्या आप सिर्फ अपने लिए coverage चाहते हैं, या family members को भी include करना चाहते हैं?
Human: Family members will be
Chat Bot: Perfect, बस एक बात confirm कर दीजिए - आप कितने family members को policy में include करना चाहते हैं?
Human: सिर्फ एक, me and my wife.
Chat Bot: आपकी और आपकी wife की age क्या है?
Human: Mary Chovan sal.
Chat Bot: धन्यवाद! मेरे record के अनुसार आपका pincode six zero zero zero one four है, क्या यह सही है?
Human: हां.
Chat Bot: धन्यवाद, आगे बढ़ते हैं। क्या आप या आपकी wife कोई regular physical activity करते हैं जैसे walking, yoga, gym?
Human: नहीं नहीं नहीं generally sir walking.
Chat Bot:
Human: Walking and सिर्फ generally walking करते हैं हम.
Human: हां diabetes diabetes है.
Chat Bot: समझ गया, यह जानकर अच्छा लगा! क्या आपकी या आपकी wife की कोई pre-existing health conditions हैं जैसे asthma, diabetes, blood pressure, heart issues, या
Chat Bot: <silence>
Chat Bot: मुझे यह सुनकर खेद है कि आपकी wife को diabetes है.
Chat Bot: हम आपकी सहायता के लिए उचित नीति खोजने का प्रयास करेंगे जो आपके स्वास्थ्य की ज़रूरतों को पूरा करे.
Chat Bot: क्या आप currently किसी health condition के लिए कोई medications ले रहे हैं?
Human: Diabetes or cholesterol.
Chat Bot: समझ गया, आपकी स्थिति के लिए हम बहुत समझ रखते हैं। क्या आप या आपकी wife recently hospitalized हुए हैं या क्या किसी doctor नै recently किसी medical condition के लिए surgery या treatment recommend किया है?
Human: नहीं नहीं, January में मैं hospitalized हुआ था.
Human: Stone के लिए.
Human: Removal of kidney stone के लिए.
Chat Bot: मुझे खेद है कि आपको यह अनुभव करना पड़ा। हम आपकी स्थिति को बेहतर ढंग से समर्थन करने के लिए यहाँ हैं। आपकी जानकारी के आधार पर, मैं आपको Activ One V-Y-T-L policy की सिफारिश
कर सकता हूँ। यह policy Day one से ही chronic diseases, जैसे diabetes, cover करती है और
chronic management program भी प्रदान करती है। क्या आप इसके बारे में और जानना चाहेंगे?
Human: हम बताइए.
Chat Bot: Activ One VYTL policy पूरी तरह से peace of mind के लिए विशेष रूप से design की गई है,
खासकर उन लोगों के लिए जिनको chronic problems हैं या होने का chance है.
Chat Bot: इसमें Day one से ही chronic diseases जैसे कि diabetes, BP, asthma, और अन्य
conditions बिना किसी waiting period के cover होती है.
Chat Bot: इसमें chronic management program भी शामिल है जिसमें personal health coach,
specialist consultation, और जरूरी tests शामिल हैं आपकी chronic illness को manage करने के लिए.
Chat Bot: इसके अलावा, consumables cover किए जाते हैं, और आप up to hundred percent health
returns भी earn कर सकते हैं जो अगले साल के renewal premium को कम करता है.
Chat Bot: क्या आप इसके साथ आगे बढ़ना चाहेंगे या इसमें कोई और जानकारी चाहिए?
Chat Bot: Hello?
Human: बताइए बताइए.
Chat Bot: Activ One VYTL policy के मुख्य फायदे हैं:
Chat Bot: - Day One से ही chronic diseases cover करती है जैसे कि आपकी diabetes और अन्य
conditions.
Chat Bot: - इसमें chronic management program मिलता है जिसमें personal health coach और
specialist consultations शामिल हैं.
Chat Bot: - Consumables जैसे gloves और PPE covered होते हैं.
Chat Bot: - Up to hundred percent health returns जो अगले साल के renewal premium को कम
करते हैं.
Chat Bot: इस policy के साथ proceed करने के लिए क्या मैं आपके registered email address पर
quotation भेज सकता हूँ?
Chat Bot: Hello?
Human: हां, quotation भेजी है.
Human: Existing हां.
Chat Bot: Great! हमारे पास आपका email address
Human: PED के लिए print
Chat Bot: <silence>
Human: waiting period क्या है?
Chat Bot: Activ One VYTL policy के तहत pre-existing diseases के लिए waiting period तीन साल
है। लेकिन खास बात यह है कि chronic diseases जैसे diabetes Day One से ही cover होती हैं। क्या मैं
आपके लिए कुछ और assist कर सकता हूँ?
Human: उसके लिए reduction नहीं हो सकता.
Human: तीन साल से.
Human: कम
Chat Bot: खेद है, लेकिन Activ One VYTL policy में pre-existing diseases के लिए जो standard
waiting period है, उसे reduce करने का कोई option नहीं है। यह standard policy term का हिस्सा है।
आपका कोई और सवाल है तो बताएं, मैं आपकी मदद करने के लिए यहाँ हूँ।
Chat Bot: Hello?
Human: Hello?
Chat Bot: नमस्ते, मैं Rahul बोल रहा हूँ aditya birla health insurance से, क्या मैं Haradhan से बात
कर रहा हूँ?
Human: हां हां बोलिए ना.
        """,
        # Transcript 4 (Chetan)
        """
Chat Bot: नमस्ते, मैं Rahul बोल रहा हूँ aditya birla health insurance से, क्या मैं Chetan Thakur से बात कर रहा हूँ?
Human: हां जी.
Chat Bot:
Human: हां जी.
Chat Bot: Great,Thank you. Please note यह Call quality और monitoring purpose के लिए
record की जा रही है.
Chat Bot: Hope that's fine?
Human: बताइए हां.
Chat Bot: Actually हमें आपकी request Aditya Birla Health Insurance website से मिली है.
Chat Bot: तो मैं बस यह check करना चाहता था कि क्या आप fresh policy की तलाश में हैं, या अपनी
existing policy को port करने के बारे में सोच रहे हैं?
Human: Main pastilla.
Human: पैसे देख रहा था.
Chat Bot: अच्छा, तो आप नई health insurance policy लेना चाहते हैं, आपको better guide करने के
लिए - क्या आप सिर्फ अपने लिए coverage चाहते हैं, या family members को भी include करना चाहते
हैं?
Human: Family है.
Chat Bot: Perfect, बस एक बात confirm कर दीजिए - आप कितने family members को policy में
include करना चाहते हैं?
Human: मैं हूं मेरी wife और एक बच्चा है.
Chat Bot: Great, क्या आप अपने सभी family members की ages बता सकते हैं?
Human: मेरे उनतीस साल है, wife की छब्बीस है.
Human: और बच्चा भी पांच महीने का है.
Chat Bot: धन्यवाद, Confirm करने के लिए, क्या two zero one three zero one आप के city का सही
pincode है?
Human: Two zero हां यही है.
Chat Bot: Perfect! Main अब next step पर proceed करता हूं.
Chat Bot: अच्छा, क्या आप या आपके family members कोई regular exercise या wellness routine
follow करते हैं - जैसे yoga, walking, या कुछ और?
Human: हमारा जैसे घर का ही काम है जो खेती बाड़ी है तो इस ही नहीं पड़ती.
Chat Bot: समझ गया. क्या आपकी या आपके family members की कोई pre-existing health
conditions हैं जैसे asthma, diabetes, BP, heart problems, या कुछ और
Human: नहीं कुछ नहीं सब fit है. सब fit है. सब fit है.
Chat Bot: <silence>
Chat Bot: अच्छा
Human: कुछ भी नहीं अभी तो फ़िलहाल सब fit है.
Chat Bot: <silence>
Chat Bot: Great, और आगे बढ़ने के लिए-
Human: मैं कैसे बात कर रहा हूं normal बात कर रहा या लग रहा sir computer से वापस सुबह
        """
    ]

    # Define prompt_rules based on the provided document
    prompt_rules = {
        'steps': {
            1: {
                'name': 'Taking consent',
                'bot_dialogues_keywords': ['call is being recorded', 'quality and monitoring purposes', 'fine'],
                'expected_human_confirmations': ['yes', 'हां', 'हाँ', 'जी', 'ok', 'okay', 'ठीक', 'fine', 'yeah'],
                'required_info': []
            },
            2: {
                'name': 'Policy Type Inquiry',
                'bot_dialogues_keywords': ['request', 'website', 'fresh policy', 'existing policy', 'port', 'नई policy', 'new', 'fresh policy', 'new one', 'कल ही ली है', 'अभी ली है', 'just yesterday', 'recently bought', 'नया policyholder हूँ', 'port', 'migrate', 'किसी और कंपनी से port', 'existing policy दूसरी कंपनी की', 'मेरे पास ABHI की policy है', 'Aditya Birla का ग्राहक हूँ', 'existing policy है', 'अपनी policy में बदलाव', 'add members', 'check status'],
                'expected_human_confirmations': ['yes', 'no', 'ठीक', 'correct', 'हां', 'हाँ'],
                'required_info': ['policy_type_interest'],
                'human_intents': {
                    'new_policy': ['नई policy', 'new', 'fresh policy', 'new one'],
                    'recent_purchase': ['कल ही ली है', 'अभी ली है', 'just yesterday', 'recently bought', 'नया policyholder हूँ'],
                    'porting': ['port', 'migrate', 'किसी और कंपनी से port', 'existing policy दूसरी कंपनी की'],
                    'existing_abhi': ['मेरे पास ABHI की policy है', 'Aditya Birla का ग्राहक हूँ', 'existing policy है', 'अपनी policy में बदलाव', 'add members', 'check status']
                }
            },
            3: {
                'name': 'Acknowledge New Policy Interest',
                'bot_dialogues_keywords': ['नई health insurance policy', 'guide', 'coverage', 'family members', 'अपने लिए coverage', 'family members को भी include'],
                'expected_human_confirmations': ['yes', 'no', 'ठीक', 'correct', 'हां', 'हाँ'],
                'required_info': ['coverage_scope']
            },
            4: {
                'name': 'Acknowledge Portability Interest',
                'bot_dialogues_keywords': ['current health insurance policy को port', 'help कर सकते हैं', 'current insurer का नाम', 'policy details share'],
                'expected_human_confirmations': ['yes', 'no', 'ठीक', 'correct', 'हां', 'हाँ'],
                'required_info': ['insurer_details', 'policy_details_for_port']
            },
            5: {
                'name': 'Family Coverage Details',
                'bot_dialogues_keywords': ['family members', 'include'],
                'expected_human_confirmations': ['yes', 'no', 'ठीक', 'correct', 'हां', 'हाँ'],
                'required_info': ['num_family_members']
            },
            6: {
                'name': 'Collect Family Ages',
                'bot_dialogues_keywords': ['सभी की ages collect', 'customer\'s age', 'family members ages'],
                'expected_human_confirmations': ['yes', 'no', 'ठीक', 'correct', 'हां', 'हाँ'],
                'required_info': ['age']
            },
            7: {
                'name': 'Collect Self Age',
                'bot_dialogues_keywords': ['age जान सकता हूँ', 'आगे बढ़ने के लिए'],
                'expected_human_confirmations': ['yes', 'no', 'ठीक', 'correct', 'हां', 'हाँ'],
                'required_info': ['age']
            },
            8: {
                'name': 'Pincode Confirmation',
                'bot_dialogues_keywords': ['Confirm करने के लिए', 'pincode', 'city का सही pincode', 'mistake हुई है', 'correct pincode क्या है'],
                'expected_human_confirmations': ['yes', 'no', 'that\'s right', 'incorrect', 'हां', 'हाँ', 'correct', 'yeah'],
                'required_info': ['pincode']
            },
            9: {
                'name': 'Physical Activity Inquiry',
                'bot_dialogues_keywords': ['regular physical activity', 'walking', 'yoga', 'gym', 'wellness routine', 'exercise'],
                'expected_human_confirmations': ['yes', 'no', 'ठीक', 'हां', 'हाँ', 'जी', 'walking', 'working'],
                'required_info': ['physical_activity_status']
            },
            10: {
                'name': 'Handle Personal Details Refusal',
                'bot_dialogues_keywords': ['समझ सकता हूँ', 'details सिर्फ इसलिए पूछने हैं', 'plan recommend', 'needs के साथ better fit', 'private और secure', 'general information share'],
                'expected_human_confirmations': ['yes', 'no', 'ठीक', 'हां', 'हाँ'],
                'required_info': []
            },
            11: {
                'name': 'Pre-existing Conditions Inquiry',
                'bot_dialogues_keywords': ['pre-existing health conditions', 'asthma', 'diabetes', 'blood pressure', 'heart issues', 'जानना चाहिए'],
                'expected_human_confirmations': ['yes', 'no', 'कुछ भी नहीं है', 'हां', 'हाँ', 'nothing'],
                'required_info': ['medical_conditions']
            },
            12: {
                'name': 'Current Medications Inquiry',
                'bot_dialogues_keywords': ['currently किसी health condition के लिए कोई medications ले रहे हैं', 'problem नहीं', 'privacy हमेशा respected और protected'],
                'expected_human_confirmations': ['yes', 'no', 'ठीक', 'हां', 'हाँ', 'never'],
                'required_info': ['medications_status']
            },
            13: {
                'name': 'Hospitalization History Inquiry',
                'bot_dialogues_keywords': ['कभी अस्पताल में भर्ती हुए हैं', 'recently hospitalized', 'कब था'],
                'expected_human_confirmations': ['yes', 'no', 'ठीक', 'हां', 'हाँ', 'never', 'January'], # Added January
                'required_info': ['hospitalization_history']
            },
            14: {
                'name': 'Doctor Recommendation Inquiry',
                'bot_dialogues_keywords': ['doctor ने recently किसी medical condition के लिए surgery या treatment recommend किया है', 'align करे', 'privacy हमारी priority'],
                'expected_human_confirmations': ['yes', 'no', 'ठीक', 'हां', 'हाँ'],
                'required_info': ['doctor_recommendation_status']
            },
            15: {
                'name': 'Policy Suggestion',
                'bot_dialogues_keywords': ['Based on the details', 'policy की सलाह दूंगी', 'specially young और healthy', 'कम premium में ज्यादा rewards', 'अधिक जानने में interested हैं', 'Activ Fit', 'Activ One Vital', 'Activ One Maxplus', 'Activ One N-X-T', 'Activ Health Platinum Enhanced', 'Activ Care Classic'],
                'expected_human_confirmations': ['yes', 'no', 'बोलिए', 'हां', 'हाँ', 'interested'],
                'required_info': ['policy_suggested']
            },
            16: {
                'name': 'Waiting Period Information',
                'bot_dialogues_keywords': ['waiting period', 'thirty days', 'three years', 'two years', 'initial waiting period', 'pre-existing disease waiting period', 'specific disease waiting period', 'Chronic Diseases will be covered from day one', 'email पर मिल जाएंगी'],
                'expected_human_confirmations': ['yes', 'no', 'ठीक', 'हां', 'हाँ'],
                'required_info': []
            },
            17: {
                'name': 'Policy Details Presentation',
                'bot_dialogues_keywords': ['key benefits', 'features', 'Health Returns', 'Claim Protect', 'Super Reload', 'Binge Refill', 'Activ One Maxplus policy', 'Activ Fit policy', 'Activ One Vital plan', 'Activ Care Classic plan', 'Activ One NXT plan', 'Activ Health Platinum Enhanced plan'],
                'expected_human_confirmations': ['yes', 'no', 'ठीक', 'हां', 'हाँ', 'ok'],
                'required_info': []
            },
            18: {
                'name': 'Add-on Coverage Discussion',
                'bot_dialogues_keywords': ['add-on coverages', 'optional', 'broader protection', 'peace of mind', 'Chronic Care', 'Chronic Management Program', 'Durable Equipment Cover', 'Second Medical Opinion', 'Cancer Booster', 'Vaccine Cover', 'Tele-O-P-D Consultation', 'Cashless Anywhere', 'Room Rent'],
                'expected_human_confirmations': ['yes', 'no', 'ठीक', 'हां', 'हाँ'],
                'required_info': ['add_on_interest']
            },
            19: {
                'name': 'Policy Summary and Confirmation',
                'bot_dialogues_keywords': ['summary provide', 'confirm', 'proceed', 'application', 'Health Returns', 'Claim Protect', 'Super Reload', 'Super Credit'],
                'expected_human_confirmations': ['yes', 'no', 'ठीक', 'हां', 'हाँ', 'ji hai'],
                'required_info': ['confirmation_to_proceed_application']
            },
            20: {
                'name': 'Quotation',
                'bot_dialogues_keywords': ['policy', 'Add-on चुन लिए हैं', 'आवेदन शुरू करना है', 'expert टीम से एक callback शेड्यूल', 'sahi premium information'],
                'expected_human_confirmations': ['yes', 'no', 'ठीक', 'हां', 'हाँ'],
                'required_info': ['callback_agreement']
            },
            21: {
                'name': 'Payment and Next Steps',
                'bot_dialogues_keywords': ['payment details', 'discuss', 'team contact', 'questions'],
                'expected_human_confirmations': ['yes', 'no', 'ठीक', 'हां', 'हाँ'],
                'required_info': []
            },
            22: {
                'name': 'Schedule Callback',
                'bot_dialogues_keywords': ['callback schedule', 'convenient time', 'tomorrow', 'morning', 'evening', 'questions', 'contact'],
                'expected_human_confirmations': ['yes', 'no', 'ठीक', 'हां', 'हाँ', 'ok', 'sure', 'yeah'],
                'required_info': ['callback_time', 'callback_date']
            },
            23: {
                'name': 'Handle Hesitation',
                'bot_dialogues_keywords': ['important decision', 'time लेना ठीक है', 'call schedule करना सही रहेगा'],
                'expected_human_confirmations': ['yes', 'no', 'ठीक', 'हां', 'हाँ'],
                'required_info': ['willing_to_schedule_callback']
            },
            24: { # Placeholder for Carney Remarks
                'name': 'Carney Remarks',
                'bot_dialogues_keywords': [],
                'expected_human_confirmations': [],
                'required_info': []
            },
            25: {
                'name': 'Customer Not Interested',
                'bot_dialogues_keywords': ['disinterest show', 'understand', 'interested क्यों नहीं', 'feedback', 'improve', 'benefits', 'health returns', 'hospital stays', 'Super Reload', 'critical illness', 'personal accident cover'],
                'expected_human_confirmations': ['yes', 'no', 'ठीक', 'हां', 'हाँ'],
                'required_info': []
            },
            26: {
                'name': 'Renewal Inquiry',
                'bot_dialogues_keywords': ['policy renew', 'registered policy number', 'registered mobile number', 'renewal team', 'thirty six working hours'],
                'expected_human_confirmations': ['yes', 'no', 'ठीक', 'हां', 'हाँ'],
                'required_info': ['policy_number', 'mobile_number']
            },
            27: {
                'name': 'Claim Status Inquiry',
                'bot_dialogues_keywords': ['claim status', 'latest update', 'customer care', 'email'],
                'expected_human_confirmations': ['yes', 'no', 'ठीक', 'हां', 'हाँ'],
                'required_info': []
            },
            28: {
                'name': 'Busy - Email Details Request',
                'bot_dialogues_keywords': ['busy', 'details email', 'send', 'key benefits quickly explain'],
                'expected_human_confirmations': ['yes', 'no', 'ठीक', 'हां', 'हाँ'],
                'required_info': ['agreement_to_explain_benefits']
            },
            29: {
                'name': 'Busy - Quick Wrap Up',
                'bot_dialogues_keywords': ['busy', 'unavailable', 'right time', 'quickly wrap up', 'two minutes'],
                'expected_human_confirmations': ['yes', 'no', 'ठीक', 'हां', 'हाँ'],
                'required_info': []
            },
            30: {
                'name': 'Voicemail',
                'bot_dialogues_keywords': ['voicemail', 'call back', 'health plans explore', 'right coverage'],
                'expected_human_confirmations': [],
                'required_info': []
            },
            31: {
                'name': 'Top Up Existing Policy',
                'bot_dialogues_keywords': ['existing policy', 'top-up plan', 'enhance', 'add members', 'सदस्य जोड़ना', 'financial cushion', 'deductible', 'large medical bills', 'renewal', 'claim status'],
                'expected_human_confirmations': ['yes', 'no', 'correct', 'ठीक', 'हां', 'हाँ'],
                'required_info': ['top_up_interest']
            },
            32: {
                'name': 'Wrong Number',
                'bot_dialogues_keywords': ['wrong number', 'concerned person', 'health insurance', 'confirm'],
                'expected_human_confirmations': ['yes', 'no', 'ठीक', 'हां', 'हाँ'],
                'required_info': ['wrong_number_status']
            },
            33: {
                'name': 'Already Received Call',
                'bot_dialogues_keywords': ['contacted', 'full policy presentation', 'check'],
                'expected_human_confirmations': ['yes', 'no', 'ठीक', 'हां', 'हाँ'],
                'required_info': ['presentation_status']
            },
            34: {
                'name': 'Presentation Done',
                'bot_dialogues_keywords': ['policy details', 'purchase', 'proceed'],
                'expected_human_confirmations': ['yes', 'no', 'ठीक', 'हां', 'हाँ'],
                'required_info': ['purchase_intent']
            },
            35: {
                'name': 'Unable to Hear',
                'bot_dialogues_keywords': ['hear', 'audible', 'callback'],
                'expected_human_confirmations': ['yes', 'no', 'ठीक', 'हां', 'हाँ'],
                'required_info': []
            },
            36: {
                'name': 'Not Visited Website',
                'bot_dialogues_keywords': ['benefits', 'health returns', 'hospital stays', 'Super Reload', 'critical illness', 'personal accident cover', 'health insurance तलाश'],
                'expected_human_confirmations': ['yes', 'no', 'ठीक', 'हां', 'हाँ'],
                'required_info': ['interest_in_insurance']
            },
            37: {
                'name': 'Financial Issues',
                'bot_dialogues_keywords': ['budget', 'affordable', 'health returns', 'premium', 'GST', 'non-medical hospital expenses', 'Super Reload', 'critical illness', 'personal accident cover'],
                'expected_human_confirmations': ['yes', 'no', 'ठीक', 'हां', 'हाँ'],
                'required_info': []
            },
            38: {
                'name': 'Payment Push',
                'bot_dialogues_keywords': ['payment process', 'challenges', 'time based offer', 'issues', 'decline'],
                'expected_human_confirmations': ['yes', 'no', 'ठीक', 'हां', 'हाँ'],
                'required_info': []
            },
            39: {
                'name': 'Language Other Than English/Hindi',
                'bot_dialogues_keywords': ['preferred language', 'team contact', 'acceptable'],
                'expected_human_confirmations': ['yes', 'no', 'ठीक', 'हां', 'हाँ'],
                'required_info': ['preferred_language']
            },
            40: {
                'name': 'Busy - WhatsApp Details Request',
                'bot_dialogues_keywords': ['WhatsApp', 'details', 'number', 'confirm', 'key benefits quickly explain'],
                'expected_human_confirmations': ['yes', 'no', 'ठीक', 'हां', 'हाँ'],
                'required_info': ['whatsapp_number_confirmation']
            },
            41: {
                'name': 'Goodbye',
                'bot_dialogues_keywords': ['Goodbye', 'time के लिए धन्यवाद', 'follow up', 'questions', 'संपर्क करें', 'Have a great day'],
                'expected_human_confirmations': [],
                'required_info': []
            }
        }
    }

    # Ensure all steps from 1 to 41 are in prompt_rules, even if placeholder, to ensure flow tracker works correctly.
    for i in range(1, 42): # Assuming max step is 41
        if i not in prompt_rules['steps']:
            prompt_rules['steps'][i] = {
                'name': f'Placeholder Step {i}',
                'bot_dialogues_keywords': [],
                'expected_human_confirmations': [],
                'required_info': []
            }

    # Re-sort steps in prompt_rules for consistent access
    prompt_rules['steps'] = dict(sorted(prompt_rules['steps'].items()))

    # Initialize NLP processor (once for all transcripts)
    nlp_processor = EnhancedNLPProcessor()

    # Dictionary to store processed data for each customer
    customer_reports_data = {} # Key: customer_name, Value: {'processed_transcript_df', 'call_report_data'}

    print("\n--- Processing Transcripts ---")
    for idx, transcript_raw in enumerate(transcripts_raw):
        customer_name = extract_customer_name(transcript_raw)
        print(f"\nProcessing call for: {customer_name} (Transcript {idx+1}/{len(transcripts_raw)})")

        # Parse the current transcript
        parsed_transcript = []
        current_time = datetime.now()
        for line in transcript_raw.strip().split('\n'):
            line = line.strip()
            if line.startswith("Chat Bot:"):
                speaker = "Chat Bot"
                utterance = line.replace("Chat Bot:", "").strip()
            elif line.startswith("Human:"):
                speaker = "Human"
                utterance = line.replace("Human:", "").strip()
            else:
                continue # Skip empty or malformed lines

            # Filter out <silence> tags
            utterance = utterance.replace("<silence>", "").strip()
            if not utterance:
                continue

            parsed_transcript.append({
                'speaker': speaker,
                'utterance': utterance,
                'timestamp': current_time # Assign a sequential timestamp for plotting
            })
            current_time += timedelta(seconds=5) # Increment time for next turn

        # Initialize conversation flow tracker for each transcript
        flow_tracker = EnhancedConversationFlowTracker(prompt_rules)

        processed_transcript_data_single_call = []

        # Initialize start_time and end_time for call_report_data
        call_start_time = parsed_transcript[0]['timestamp'] if parsed_transcript else datetime.now()
        call_end_time = parsed_transcript[-1]['timestamp'] if parsed_transcript else datetime.now()

        call_report_data_single_call = {
            'call_id': f'call_{customer_name}_{idx+1}',
            'customer_name': customer_name,
            'start_time': call_start_time,
            'end_time': call_end_time,
            'duration_seconds': (call_end_time - call_start_time).total_seconds() if parsed_transcript else 0,
            'total_turns': len(parsed_transcript),
            'speakers': list(set([t['speaker'] for t in parsed_transcript])),
            'collected_entities': {},
            'sentiment_summary': {},
            'language_summary': {},
            'conversation_flow_data': {},
            'bot_suggested_policy': None
        }

        for i, turn in enumerate(parsed_transcript):
            speaker = turn['speaker']
            utterance = turn['utterance']
            turn_index = i + 1

            extracted_entities = nlp_processor.extract_entities_advanced(utterance, speaker)

            sentiment_analysis = nlp_processor.analyze_sentiment_advanced(utterance)
            language_detection = nlp_processor.detect_language_advanced(utterance)

            # Update conversation state
            flow_tracker.update_conversation_state(speaker, utterance, extracted_entities, turn_index)

            # Store data for this turn
            processed_turn = {
                'turn_index': turn_index,
                'speaker': speaker,
                'utterance': utterance,
                'timestamp': turn['timestamp'],
                'extracted_entities': extracted_entities,
                'sentiment_compound': sentiment_analysis['compound'],
                'sentiment_emotion': sentiment_analysis['emotion'],
                'language': language_detection['language'],
                'language_confidence': language_detection['confidence'],
                'current_flow_step': flow_tracker.conversation_state['current_step']
            }
            processed_transcript_data_single_call.append(processed_turn)

        # After processing all turns, finalize call report data for this single call
        call_report_data_single_call['conversation_flow_data'] = flow_tracker.get_conversation_flow_data()
        call_report_data_single_call['collected_entities'] = flow_tracker.conversation_state['collected_entities']

        # Summarize sentiment and language from processed_transcript_data
        df_transcript_single_call = pd.DataFrame(processed_transcript_data_single_call)

        if not df_transcript_single_call.empty:
            call_report_data_single_call['sentiment_summary'] = {
                'avg_compound_human': df_transcript_single_call[df_transcript_single_call['speaker'] == 'Human']['sentiment_compound'].mean(),
                'avg_compound_bot': df_transcript_single_call[df_transcript_single_call['speaker'] == 'Chat Bot']['sentiment_compound'].mean(),
                'overall_emotion_counts': df_transcript_single_call['sentiment_emotion'].value_counts().to_dict()
            }
            call_report_data_single_call['language_summary'] = {
                'language_counts_human': df_transcript_single_call[df_transcript_single_call['speaker'] == 'Human']['language'].value_counts().to_dict(),
                'language_counts_bot': df_transcript_single_call[df_transcript_single_call['speaker'] == 'Chat Bot']['language'].value_counts().to_dict(),
                'total_language_counts': df_transcript_single_call['language'].value_counts().to_dict()
            }

            # Attempt to derive bot suggested policy for THIS CALL
            # Look for specific phrasing in Step 15 where bot suggests a policy
            bot_suggested_policies_current_call = [] # List for policies suggested in THIS call
            for t in processed_transcript_data_single_call:
                if t['speaker'] == 'Chat Bot' and t['current_flow_step'] == 15:
                    # Check for explicit suggestion phrasing from the prompt: "मैं "Activ Fit" policy की सलाह दूंगी."
                    suggestion_match = re.search(r'मैं "(Activ Fit|Activ One Maxplus|Activ One Vital|Activ One N-X-T|Activ Health Platinum Enhanced|Activ Care Classic)" policy की सलाह दूंगी', t['utterance'], re.IGNORECASE)
                    if suggestion_match:
                        bot_suggested_policies_current_call.append(suggestion_match.group(1))

                    # Also check for Activ One V-Y-T-L policy which might be suggested directly by name in the text
                    # Example: "मैं आपको Activ One V-Y-T-L policy की सिफारिश कर सकता हूँ"
                    elif "Activ One V-Y-T-L" in t['utterance'] or "Activ One Vital" in t['utterance']: # Ensure both forms are caught
                        if "Activ One Vital" not in bot_suggested_policies_current_call: # Avoid duplicates
                            bot_suggested_policies_current_call.append("Activ One Vital")

                    # If no exact match, check if any recognized policy name is mentioned in the utterance
                    for policy_name_in_ent in t['extracted_entities'].get('policies_mentioned', []):
                        if policy_name_in_ent not in bot_suggested_policies_current_call:
                            bot_suggested_policies_current_call.append(policy_name_in_ent)

            # Only set bot_suggested_policy if a unique policy was clearly suggested
            if bot_suggested_policies_current_call:
                call_report_data_single_call['bot_suggested_policy'] = bot_suggested_policies_current_call[0] # Take the first suggested policy

            # Store data for the current customer
            customer_reports_data[customer_name] = {
                'processed_transcript_df': df_transcript_single_call,
                'call_report_data': call_report_data_single_call
            }

    # Instantiate and use the dashboard with all customer data
    dashboard = EnhancedABHIVisualizationDashboard(customer_reports_data, nlp_processor, prompt_rules)

    # Generate and show plots
    print("\n📊 Generating Combined Visualizations...")

    fig_heatmap = dashboard.create_enhanced_conversation_flow_heatmap()
    fig_heatmap.show()

    fig_policy_chart = dashboard.create_enhanced_policy_entity_chart()
    fig_policy_chart.show()

    fig_sentiment_time = dashboard.create_sentiment_over_time_chart()
    fig_sentiment_time.show()

    fig_language_dist = dashboard.create_language_distribution_chart()
    fig_language_dist.show()

    fig_entity_overview = dashboard.create_entity_extraction_overview()
    fig_entity_overview.show()

    fig_step_timeline = dashboard.create_step_completion_timeline()
    fig_step_timeline.show()

    # Generate and print Prompt Adherence and Correctness Analysis
    print("\n--- Prompt Adherence and Correctness Analysis ---")
    adherence_df = dashboard.analyze_prompt_adherence_and_correctness()
    print(adherence_df.to_markdown(index=False)) # Use to_markdown for nice console output

    print("\n--- Summary of Collected Entities and Bot Suggestions Per Call ---")
    for customer_name, data in customer_reports_data.items():
        print(f"\nCustomer: {customer_name}")
        print(f"  Collected Entities: {json.dumps(data['call_report_data']['collected_entities'], indent=2, ensure_ascii=False)}")
        print(f"  Bot Suggested Policy: {data['call_report_data']['bot_suggested_policy']}")
        print(f"  Final Flow State: Current Step {data['call_report_data']['conversation_flow_data']['current_step']}, Completed Steps {data['call_report_data']['conversation_flow_data']['completed_steps']}")


