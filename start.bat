@echo off
echo Starting Transcript Analysis System...

echo.
echo Installing Python dependencies...
pip install -r requirements.txt

echo.
echo Starting Flask API server...
start "Flask API" cmd /k "python api.py"

echo.
echo Waiting for API to start...
timeout /t 3 /nobreak > nul

echo.
echo Starting React development server...
npm run dev

echo.
echo Both servers are now running!
echo Frontend: http://localhost:5173
echo API: http://localhost:5000
echo.
echo Press any key to exit...
pause > nul 