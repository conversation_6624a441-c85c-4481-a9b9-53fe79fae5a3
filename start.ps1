Write-Host "Starting Transcript Analysis System..." -ForegroundColor Green

Write-Host "`nInstalling Python dependencies..." -ForegroundColor Yellow
pip install -r requirements.txt

Write-Host "`nStarting Flask API server..." -ForegroundColor Yellow
Start-Process powershell -ArgumentList "-NoExit", "-Command", "python api.py" -WindowStyle Normal

Write-Host "`nWaiting for API to start..." -ForegroundColor Yellow
Start-Sleep -Seconds 3

Write-Host "`nStarting React development server..." -ForegroundColor Yellow
npm run dev

Write-Host "`nBoth servers are now running!" -ForegroundColor Green
Write-Host "Frontend: http://localhost:5173" -ForegroundColor Cyan
Write-Host "API: http://localhost:5000" -ForegroundColor Cyan
Write-Host "`nPress Ctrl+C to stop the React server" -ForegroundColor Yellow 