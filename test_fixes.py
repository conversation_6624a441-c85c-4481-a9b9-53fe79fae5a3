#!/usr/bin/env python3
"""
Test script to verify the fixes for conversation flow and policy analysis
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src', 'utils'))

from transcriptAnalyzer import AdityaBirlaTranscriptAnalysisSystem

def test_swayam_conversation():
    """Test the Swayam conversation to check if closing step is properly handled"""
    
    # Swayam's transcript
    swayam_transcript = """Chat Bot: नमस्ते,मैं Rahul  बोल रहा  हूँ aditya birla health insurance से, क्या मैं Swayam से बात कर रहा हूँ?
Human:  Yes. This is Sam.
Chat Bot: Thank you, Sam.
Chat Bot: Please note इस call को quality और monitoring purpose के लिए record किया जा रहा है.
Chat Bot: Hope that's fine?
Human:  Yeah. That's fine.
Chat <PERSON>t: Actually हमें आपकी request Aditya Birla Health Insurance website से मिली है.
Chat Bot: तो मैं बस यह check करना चाहता था कि क्या आप fresh policy की तलाश में हैं, या अपनी existing policy को port करने के बारे में सोच रहे हैं?
Human:  No. I don't have a policy as of now.
Chat Bot: अच्छा, तो आप नई health insurance policy लेना चाहते हैं। आपको better guide करने के लिए - क्या आप सिर्फ अपने लिए coverage चाहते हैं, या family members को भी include करना चाहते हैं?
Human:  No. So I wanted to understand, like, doesn't this really work after I grow to a certain age like today I have no health condition but later on I may need some cover. So I just wanted to understand why I need health insurance.
Chat Bot: Health insurance आपके लिए एक financial safety net की तरह काम करता है, जो आपको unexpected medical expenses से protect करता है.
Chat Bot: आज भले ही आपको कोई health condition न हो, लेकिन future में health risks बढ़ सकते हैं जैसे age increase होने के साथ, और कभी-कभी अचानक hospitalization या medical treatment की जरूरत भी पड़ सकती है, जो काफी महंगी हो सकती है.
Chat Bot: Health insurance की वजह से न सिर्फ आप financial burdens से बचे रह सकते हैं, बल्कि आप time पर और बिना चिंता किए आवश्यक medical care भी ले सकते हैं.
Chat Bot: इसके साथ ही, कुछ policies health management benefits भी offer करती हैं जैसे annual health checkups, जिसके तहत आप अपनी health का regular checkup maintain कर सकते हैं.
Chat Bot: बीमारी या medical emergencies कभी भी आ सकती हैं.
Chat Bot: Health insurance आपके लिए एक financial safety net की तरह काम करता है, जो आपको
Human:  So how much will it so how much will you charge me with?
Chat Bot: Great, आगे बढ़ने के लिए  आपकी कुछ details चाहिए होंगी जिससे कि हम आपकी specific needs के अनुसार सही policy recommend कर सकें। क्या मैं आपकी age जान सकता हूँ?
Human:  No. I shared my age, but, like, what if I change job or I'm changing locations? Would my policy still old?
Chat Bot: Yes, absolutely. Once you have a health insurance policy, it remains valid even if you change jobs or relocate. Your policy is with you regardless of your employment status or location. You just need to make sure to inform us about your new address or any other contact details so that your records stay updated for any communication. Do you have any other questions or would you like to proceed with exploring policy
Human:  So let's say I make the payment today, you will not ask me for money again next year or the year after that, right? Like once I make a health insurance payment, it there. Like like like a life insurance.
Chat Bot: <silence>
Chat Bot: Health insurance का purpose different होता है.
Chat Bot: Health insurance एक annual contract की तरह होता है, जिसका मतलब है कि आपको हर साल policy renew करनी होगी और इसके लिए annual premium pay करना होगा.
Chat Bot: Health insurance life insurance की तरह one-time payment या whole life coverage की तरह नहीं होता है.
Chat Bot: Annual renewal की process से policyholder की needs, premium, और coverages को साल दर साल adjust किया जा सकता है.
Chat Bot: इस तरीके से, आप अपनी policy में बदलाव भी कर सकते हैं, जैसे coverage amount adjust करना या add-on benefits लेना.
Chat Bot: अगर आप ज्यादा details में जानना चाहते हैं या एक specific quote देखना चाहते हैं, तो मैं आपकी age और कुछ basic information collect करके आपको best options बता सकता हूं.
Human:  No. So, like, can I sort of make a payment today? But, you know, because I don't have a condition, can I pause it and then restart it, let's say, one or two years later?
Chat Bot: Health insurance policies generally can't be paused and restarted like that.
Chat Bot: They need to be maintained continuously through regular premium payments.
Chat Bot: If you skip a renewal, the policy lapses, and you lose all accumulated benefits, like waiting period credit and coverage continuity.
Chat Bot: If you choose to get a policy again later, you'd need to reapply and go through the waiting periods anew.
Chat Bot: It's often more beneficial to keep your policy active consistently to maintain continuous coverage, especially since health issues can arise unexpectedly.
Chat Bot: Do you have any other questions about health insurance policies or coverage?
Human:  So can I add my partner, like, after I get married?
Chat Bot: Yes, absolutely! After you get married, you can definitely add your partner to your existing health insurance policy.
Chat Bot: This typically involves notifying your insurance provider about the change in your family status and submitting any required documents related to your marriage.
Chat Bot: It's a good way to ensure that both you and your partner have comprehensive health coverage under one plan.
Chat Bot: If you need help with this process or have any more questions, feel free to ask!
Human:  So, like, today, have bought a plan and, you know, there's some waiting period that I've already, you know, sort of waited for. And then I get married. So do that applies to my partner as well. Like, she will not have to then go through the waiting period again.
Chat Bot: When you add your partner to your existing health insurance policy, she would typically have her own waiting periods that would need to be completed, even if you have already completed yours.
Chat Bot: The waiting periods apply individually to each policy member.
Chat Bot: However, some insurance providers might offer certain terms or discounts based on your existing coverage, so it's always good to check the specific terms with your insurance provider.
Chat Bot: If you have any questions or need further assistance on this, feel free to ask.
Human:  So who is this insurance provider who can give me this?
Chat Bot: मैं सिर्फ Aditya Birla Health Insurance का AI representative हूं.
Chat Bot: क्या आपको Health Insurance से जुड़ा कोई doubt है?
Human:  So which insurance provider will allow give me this discount?
Human:  Tata दे देगा यह?
Chat Bot: मैं सिर्फ Aditya Birla Health Insurance का representative हूं.
Chat Bot: क्या आपको Aditya Birla Health Insurance के बारे में कोई doubt है?
Human:  Link with our Thank you.
Human:  So much.
Human:  Bye.
Chat Bot: Goodbye."""

    # Initialize the analysis system
    analyzer = AdityaBirlaTranscriptAnalysisSystem()
    
    # Analyze the transcript
    result = analyzer._analyze_single_transcript(swayam_transcript, "Swayam")
    
    print("=== SWAYAM CONVERSATION ANALYSIS ===")
    print(f"Customer Name: {result.customer_name}")
    print(f"Conversation Flow Steps: {result.conversation_flow.step_names}")
    print(f"Completion Rate: {result.conversation_flow.completion_rate:.2f}%")
    print(f"Adherence Score: {result.conversation_flow.adherence_score:.2f}%")
    print(f"Policy Analysis - Expected: {result.policy_analysis.expected}")
    print(f"Policy Analysis - Actual: {result.policy_analysis.actual}")
    print(f"Policy Match: {result.policy_analysis.match}")
    print(f"Outcome: {result.outcome}")
    print()

def test_ibrahim_policy():
    """Test the Ibrahim policy analysis to check if VYTL vs Vital is handled correctly"""
    
    # Create a test transcript with Activ One VYTL mention
    ibrahim_transcript = """Chat Bot: Hello, I'm calling from Aditya Birla Health Insurance. Am I speaking to Ibrahim?
Human: Yes, this is Ibrahim.
Chat Bot: Thank you Ibrahim. I see you have some health conditions including diabetes and asthma.
Chat Bot: Based on your profile, I would recommend Activ One VYTL policy for you.
Chat Bot: This policy provides excellent coverage for chronic conditions.
Human: Thank you for the recommendation.
Chat Bot: You're welcome. Goodbye."""

    # Initialize the analysis system
    analyzer = AdityaBirlaTranscriptAnalysisSystem()
    
    # Create customer data for Ibrahim with health conditions
    from transcriptAnalyzer import CustomerData
    ibrahim_data = CustomerData(
        name="Ibrahim",
        age=45,
        health_conditions=["diabetes", "asthma", "blood pressure", "heart", "Stone", "kidney", "stone"]
    )
    
    # Test policy analysis directly
    policy_analyzer = analyzer.policy_analyzer
    policy_analysis = policy_analyzer.analyze_policy_accuracy(ibrahim_data, ibrahim_transcript)
    
    print("=== IBRAHIM POLICY ANALYSIS ===")
    print(f"Expected Policy: {policy_analysis.expected}")
    print(f"Actual Policy: {policy_analysis.actual}")
    print(f"Policy Match: {policy_analysis.match}")
    print(f"Confidence: {policy_analysis.confidence:.2f}")
    print(f"Reasoning: {policy_analysis.reasoning}")
    print()

if __name__ == "__main__":
    test_swayam_conversation()
    test_ibrahim_policy() 